Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.31f1 (3409e2af086f) revision 3410402'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32508 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
G:\2021.3.31f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
G:/Work/XRG_SVN/Mini Game
-logFile
Logs/AssetImportWorker0.log
-srvPort
56668
Successfully changed project path to: G:/Work/XRG_SVN/Mini Game
G:/Work/XRG_SVN/Mini Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [30688] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3430971814 [EditorId] 3430971814 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9EDR9P8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [30688] Host "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3430971814 [EditorId] 3430971814 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-9EDR9P8) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 69.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.31f1 (3409e2af086f)
[Subsystems] Discovering subsystems at path G:/2021.3.31f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/Work/XRG_SVN/Mini Game/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 (ID=0x2504)
    Vendor:   NVIDIA
    VRAM:     12115 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'G:/2021.3.31f1/Editor/Data/Managed'
Mono path[1] = 'G:/2021.3.31f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'G:/2021.3.31f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56592
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: G:/2021.3.31f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001220 seconds.
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 285 ms
Refreshing native plugins compatible for Editor in 81.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.782 seconds
Domain Reload Profiling:
	ReloadAssembly (782ms)
		BeginReloadAssembly (60ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (0ms)
		EndReloadAssembly (668ms)
			LoadAssemblies (60ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (91ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (21ms)
			SetupLoadedEditorAssemblies (529ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (353ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (81ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (59ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.001907 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 73.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.196 seconds
Domain Reload Profiling:
	ReloadAssembly (1196ms)
		BeginReloadAssembly (88ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (15ms)
		EndReloadAssembly (1041ms)
			LoadAssemblies (71ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (185ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (43ms)
			SetupLoadedEditorAssemblies (730ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (74ms)
				BeforeProcessingInitializeOnLoad (54ms)
				ProcessInitializeOnLoadAttributes (539ms)
				ProcessInitializeOnLoadMethodAttributes (44ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4115 Unused Serialized files (Serialized files now loaded: 0)
Unloading 25 unused Assets / (342.5 KB). Loaded Objects now: 4589.
Memory consumption went from 158.2 MB to 157.9 MB.
Total: 3.441800 ms (FindLiveObjects: 0.321000 ms CreateObjectMapping: 0.232600 ms MarkObjects: 2.660500 ms  DeleteObjects: 0.226000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5852.186497 seconds.
  path: Assets/Art/82b3c8a32a81c0b6bd4d44ec324685c1.png
  artifactKey: Guid(8e3738259eedc674f949848cf8550529) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Art/82b3c8a32a81c0b6bd4d44ec324685c1.png using Guid(8e3738259eedc674f949848cf8550529) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c3ab5a76ec46bbfcbea2c86ee3b739df') in 0.073614 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001979 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.157 seconds
Domain Reload Profiling:
	ReloadAssembly (1158ms)
		BeginReloadAssembly (169ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (931ms)
			LoadAssemblies (81ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (189ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (629ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (521ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (7ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4596.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.702900 ms (FindLiveObjects: 0.265500 ms CreateObjectMapping: 0.183600 ms MarkObjects: 3.129100 ms  DeleteObjects: 0.123300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002189 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.089 seconds
Domain Reload Profiling:
	ReloadAssembly (1089ms)
		BeginReloadAssembly (104ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (29ms)
		EndReloadAssembly (925ms)
			LoadAssemblies (64ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (184ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (628ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (48ms)
				ProcessInitializeOnLoadAttributes (516ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4599.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.337700 ms (FindLiveObjects: 0.276000 ms CreateObjectMapping: 0.157700 ms MarkObjects: 2.817200 ms  DeleteObjects: 0.085800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001885 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.074 seconds
Domain Reload Profiling:
	ReloadAssembly (1075ms)
		BeginReloadAssembly (103ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (912ms)
			LoadAssemblies (67ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (180ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (617ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (511ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (315.0 KB). Loaded Objects now: 4602.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.225600 ms (FindLiveObjects: 0.326400 ms CreateObjectMapping: 0.222200 ms MarkObjects: 2.597800 ms  DeleteObjects: 0.078300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003361 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.071 seconds
Domain Reload Profiling:
	ReloadAssembly (1071ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (908ms)
			LoadAssemblies (69ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (186ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (606ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (499ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4605.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.250100 ms (FindLiveObjects: 0.303700 ms CreateObjectMapping: 0.181800 ms MarkObjects: 2.688400 ms  DeleteObjects: 0.074900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002330 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.093 seconds
Domain Reload Profiling:
	ReloadAssembly (1093ms)
		BeginReloadAssembly (106ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (30ms)
		EndReloadAssembly (928ms)
			LoadAssemblies (69ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (193ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (616ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (507ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4608.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.621700 ms (FindLiveObjects: 0.398100 ms CreateObjectMapping: 0.221500 ms MarkObjects: 2.913600 ms  DeleteObjects: 0.087200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001721 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.111 seconds
Domain Reload Profiling:
	ReloadAssembly (1112ms)
		BeginReloadAssembly (113ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (943ms)
			LoadAssemblies (72ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (210ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (620ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (515ms)
				ProcessInitializeOnLoadMethodAttributes (40ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4611.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 2.929400 ms (FindLiveObjects: 0.249500 ms CreateObjectMapping: 0.148200 ms MarkObjects: 2.432100 ms  DeleteObjects: 0.098400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002245 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.109 seconds
Domain Reload Profiling:
	ReloadAssembly (1109ms)
		BeginReloadAssembly (109ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (943ms)
			LoadAssemblies (73ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (196ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (35ms)
			SetupLoadedEditorAssemblies (624ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (47ms)
				ProcessInitializeOnLoadAttributes (514ms)
				ProcessInitializeOnLoadMethodAttributes (42ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4614.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.302300 ms (FindLiveObjects: 0.325300 ms CreateObjectMapping: 0.223300 ms MarkObjects: 2.626000 ms  DeleteObjects: 0.126600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002337 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.075 seconds
Domain Reload Profiling:
	ReloadAssembly (1076ms)
		BeginReloadAssembly (104ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (916ms)
			LoadAssemblies (68ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (185ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (614ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (46ms)
				ProcessInitializeOnLoadAttributes (505ms)
				ProcessInitializeOnLoadMethodAttributes (40ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4617.
Memory consumption went from 152.9 MB to 152.6 MB.
Total: 3.565900 ms (FindLiveObjects: 0.471500 ms CreateObjectMapping: 0.276100 ms MarkObjects: 2.690100 ms  DeleteObjects: 0.127000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001784 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.085 seconds
Domain Reload Profiling:
	ReloadAssembly (1086ms)
		BeginReloadAssembly (103ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (27ms)
		EndReloadAssembly (922ms)
			LoadAssemblies (69ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (186ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (38ms)
			SetupLoadedEditorAssemblies (611ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (504ms)
				ProcessInitializeOnLoadMethodAttributes (42ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4620.
Memory consumption went from 153.0 MB to 152.6 MB.
Total: 3.185900 ms (FindLiveObjects: 0.322200 ms CreateObjectMapping: 0.199900 ms MarkObjects: 2.556900 ms  DeleteObjects: 0.105600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001864 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.082 seconds
Domain Reload Profiling:
	ReloadAssembly (1082ms)
		BeginReloadAssembly (106ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (919ms)
			LoadAssemblies (69ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (185ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (618ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (44ms)
				ProcessInitializeOnLoadAttributes (511ms)
				ProcessInitializeOnLoadMethodAttributes (40ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4623.
Memory consumption went from 153.0 MB to 152.6 MB.
Total: 3.287000 ms (FindLiveObjects: 0.348900 ms CreateObjectMapping: 0.195700 ms MarkObjects: 2.640400 ms  DeleteObjects: 0.100700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.002074 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.125 seconds
Domain Reload Profiling:
	ReloadAssembly (1125ms)
		BeginReloadAssembly (125ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (934ms)
			LoadAssemblies (80ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (195ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (630ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (45ms)
				ProcessInitializeOnLoadAttributes (523ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4626.
Memory consumption went from 153.0 MB to 152.7 MB.
Total: 3.467400 ms (FindLiveObjects: 0.318400 ms CreateObjectMapping: 0.187200 ms MarkObjects: 2.847300 ms  DeleteObjects: 0.113000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.003321 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.098 seconds
Domain Reload Profiling:
	ReloadAssembly (1099ms)
		BeginReloadAssembly (112ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (28ms)
		EndReloadAssembly (924ms)
			LoadAssemblies (73ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (190ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (616ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (43ms)
				ProcessInitializeOnLoadAttributes (510ms)
				ProcessInitializeOnLoadMethodAttributes (41ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4629.
Memory consumption went from 153.0 MB to 152.7 MB.
Total: 3.537800 ms (FindLiveObjects: 0.356700 ms CreateObjectMapping: 0.249500 ms MarkObjects: 2.855600 ms  DeleteObjects: 0.074800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.001886 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  1.069 seconds
Domain Reload Profiling:
	ReloadAssembly (1069ms)
		BeginReloadAssembly (103ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (26ms)
		EndReloadAssembly (910ms)
			LoadAssemblies (69ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (182ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (614ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (21ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (45ms)
				ProcessInitializeOnLoadAttributes (506ms)
				ProcessInitializeOnLoadMethodAttributes (39ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (5ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4063 Unused Serialized files (Serialized files now loaded: 0)
Unloading 20 unused Assets / (314.9 KB). Loaded Objects now: 4632.
Memory consumption went from 153.0 MB to 152.7 MB.
Total: 3.635100 ms (FindLiveObjects: 0.320600 ms CreateObjectMapping: 0.185800 ms MarkObjects: 3.030800 ms  DeleteObjects: 0.096400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
