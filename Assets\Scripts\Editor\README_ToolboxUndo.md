# 工具箱Undo系统

## 🔧 功能概述

为预制体生成工具箱添加了完整的撤销/重做功能，支持在工具箱窗口内使用Ctrl+Z/Ctrl+Y快捷键进行撤销操作。

## 🎯 分标签页Undo系统

### 1. 区域选择标签页 🖱️
**功能**: 使用自定义Undo系统撤销区域选择操作
**快捷键**: 在工具箱窗口内按Ctrl+Z / Ctrl+Y
**范围**: 
- 点选操作
- 绘制操作
- 擦除操作
- 矩形选择
- 清除所有选择

### 2. 预制体生成标签页 🏗️
**功能**: 使用Unity原生Undo系统
**快捷键**: 标准的Ctrl+Z / Ctrl+Y（全局有效）
**范围**:
- 所有参数更改
- 预制体生成操作
- 与Unity编辑器完全集成

## 🎨 使用方式

### 在工具箱窗口内操作
```
1. 确保工具箱窗口获得焦点
2. 进行区域选择或参数调整
3. 按Ctrl+Z撤销操作
4. 按Ctrl+Y重做操作
```

### 智能事件分发
```
当前标签页        | 撤销方式           | 处理逻辑
----------------|------------------|------------------
区域选择         | 自定义Undo系统     | 工具箱拦截并处理
预制体生成       | Unity Undo系统    | 让Unity处理
```

## 🔧 技术实现

### 键盘事件处理
```csharp
private void HandleKeyboardEvents()
{
    Event e = Event.current;
    if (e.type == EventType.KeyDown)
    {
        // Ctrl+Z 撤销
        if (e.control && e.keyCode == KeyCode.Z && !e.shift)
        {
            switch (currentTab)
            {
                case ToolboxTab.AreaSelector:
                    if (areaSelectorTab.CanUndo())
                    {
                        areaSelectorTab.Undo();
                        e.Use(); // 消费事件，防止其他系统处理
                        Repaint(); // 重绘窗口
                    }
                    break;
                case ToolboxTab.PrefabGeneration:
                    // Unity Undo系统自动处理
                    break;
            }
        }
    }
}
```

### 事件消费机制
```csharp
// 关键点：消费事件防止冲突
e.Use(); // 防止Unity的其他系统处理同一个事件
Repaint(); // 立即重绘窗口显示变化
```

### 窗口焦点检测
```csharp
// 在OnGUI开始时处理键盘事件
private void OnGUI()
{
    HandleKeyboardEvents(); // 优先处理键盘事件
    
    // ... 其他UI绘制
}
```

## 🎯 使用场景

### 场景1: 区域选择撤销
```
用户操作：
1. 在区域选择标签页中绘制选区
2. 发现选择错误
3. 在工具箱窗口内按Ctrl+Z
4. 撤销最后的绘制操作

结果：快速纠正选择错误
```

### 场景2: 参数调整撤销
```
用户操作：
1. 在预制体生成标签页调整密度滑块
2. 发现参数不合适
3. 按Ctrl+Z（全局有效）
4. 撤销参数更改

结果：快速恢复之前的参数
```

### 场景3: 混合操作撤销
```
用户操作：
1. 在区域选择标签页选择区域
2. 切换到预制体生成标签页调整参数
3. 在各自标签页使用Ctrl+Z撤销
4. 每个标签页独立管理撤销历史

结果：支持复杂的工作流程
```

### 场景4: 窗口焦点管理
```
用户操作：
1. 点击工具箱窗口确保获得焦点
2. 进行区域选择操作
3. 按Ctrl+Z在工具箱内撤销
4. 点击场景视图，Ctrl+Z变为Unity全局撤销

结果：智能的焦点感知撤销
```

## 🎉 功能优势

### 1. 智能分发
- ✅ **标签页感知**: 根据当前标签页选择合适的撤销系统
- ✅ **事件消费**: 正确处理键盘事件，避免冲突
- ✅ **焦点管理**: 只在工具箱窗口有焦点时处理区域选择撤销

### 2. 无缝集成
- ✅ **区域选择**: 自定义Undo系统提供精确控制
- ✅ **参数设置**: Unity Undo系统提供原生体验
- ✅ **预制体生成**: Unity Undo系统确保完整集成

### 3. 用户友好
- ✅ **统一快捷键**: 所有操作都使用Ctrl+Z/Ctrl+Y
- ✅ **即时反馈**: 撤销操作立即生效并重绘
- ✅ **状态一致**: UI状态与撤销历史保持同步

### 4. 性能优化
- ✅ **事件优先**: 在OnGUI开始时优先处理键盘事件
- ✅ **智能重绘**: 只在必要时重绘窗口
- ✅ **内存效率**: 区域选择使用有限历史，参数使用Unity管理

## 🔍 技术细节

### 事件处理顺序
```
1. OnGUI() 开始
2. HandleKeyboardEvents() 处理键盘输入
3. 检查当前标签页
4. 分发到对应的撤销系统
5. 消费事件（如果处理了）
6. 继续正常的UI绘制
```

### 焦点管理策略
```
窗口状态          | 区域选择撤销       | 参数撤销
----------------|------------------|------------------
工具箱有焦点      | 工具箱处理         | Unity处理
工具箱无焦点      | Unity处理         | Unity处理
场景视图有焦点    | Unity处理         | Unity处理
```

### 冲突避免机制
```csharp
// 1. 检查是否可以处理
if (areaSelectorTab.CanUndo())
{
    // 2. 执行撤销
    areaSelectorTab.Undo();
    
    // 3. 消费事件，防止Unity处理
    e.Use();
    
    // 4. 重绘窗口
    Repaint();
}
// 5. 如果不能处理，让Unity处理
```

## 💡 使用建议

### 最佳实践
1. **确保焦点**: 在工具箱窗口内点击确保获得焦点
2. **标签页切换**: 在对应标签页进行撤销操作
3. **混合使用**: 区域选择用工具箱撤销，参数用全局撤销
4. **及时保存**: 重要操作后及时保存场景

### 注意事项
1. **窗口焦点**: 区域选择撤销需要工具箱窗口有焦点
2. **标签页状态**: 撤销功能与当前标签页相关
3. **事件冲突**: 正确的事件消费避免了冲突
4. **历史限制**: 区域选择有历史步数限制

## 🔧 故障排除

### 撤销不工作
- 检查工具箱窗口是否有焦点
- 确认在正确的标签页
- 检查是否有可撤销的操作

### 快捷键冲突
- 确保事件被正确消费
- 检查其他插件是否占用快捷键
- 重启Unity编辑器

### 状态不同步
- 检查Repaint()调用
- 确认撤销后UI更新
- 验证历史状态正确性

现在工具箱具有完整的撤销功能，用户可以在工具箱窗口内使用熟悉的Ctrl+Z快捷键进行撤销操作，大大提升了工作效率！
