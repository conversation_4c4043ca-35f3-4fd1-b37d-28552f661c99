# 预制体生成Undo系统

## 🔧 功能概述

为预制体生成工具添加了完整的撤销/重做功能，包括参数设置的历史记录和生成预制体的Unity Undo集成。

## 🎯 双重Undo系统

### 1. 参数Undo系统 ⚙️
**功能**: 撤销/重做所有滑块和设置参数的更改
**范围**: 
- 生成模式选择
- 密度滑块
- 最小间距设置
- 父物体设置
- 对齐地面选项
- 地面层设置
- 随机种子选项
- 预览大小滑块
- 预览列数滑块
- 高质量预览选项

### 2. 预制体生成Undo系统 🏗️
**功能**: 使用Unity内置Undo系统撤销生成的预制体
**范围**:
- 生成的所有预制体实例
- 与Unity编辑器Ctrl+Z完全集成
- 支持Unity的Undo历史面板

## 🎨 UI界面

### 参数控制按钮
```
[撤销参数] [重做参数]     [清除生成]
```

- **撤销参数**: 回退到上一个参数状态
- **重做参数**: 前进到下一个参数状态  
- **清除生成**: 删除最后一次生成的所有预制体

### 智能按钮状态
- **灰显逻辑**: 按钮根据历史状态自动启用/禁用
- **视觉反馈**: 清楚显示当前可用的操作

## 🔧 技术实现

### 参数状态数据结构
```csharp
[System.Serializable]
public class ParameterState
{
    public GenerationSettings generationSettings;
    public int previewSize;
    public int previewColumns;
    public bool useHighQualityPreviews;
    public bool includeSubfolders;
    
    // 深拷贝构造函数确保状态独立性
}
```

### 参数历史管理
```csharp
private List<ParameterState> parameterHistory = new List<ParameterState>();
private int currentParameterIndex = -1;
private const int MAX_PARAMETER_UNDO_STEPS = 30;
```

### Unity Undo集成
```csharp
// 生成开始时设置Undo组
Undo.IncrementCurrentGroup();
Undo.SetCurrentGroupName("Generate Prefabs");

// 每个预制体注册到Undo系统
Undo.RegisterCreatedObjectUndo(instance, "Generate Prefab");

// 生成完成后合并操作
Undo.CollapseUndoOperations(undoGroupIndex);
```

## 🎯 参数Undo触发时机

### 智能保存策略
```
参数类型          | 保存时机           | 原因
----------------|------------------|------------------
枚举选择         | 值改变时           | 每次选择都是独立操作
滑块拖动         | 值改变时           | 实时保存用户调整
数值输入         | 值改变时           | 精确数值是独立操作
布尔切换         | 状态改变时         | 开关状态是独立操作
对象引用         | 引用改变时         | 对象选择是独立操作
```

### 变化检测机制
```csharp
// 示例：密度滑块的变化检测
float newDensity = EditorGUILayout.Slider(generationSettings.density, 0.01f, 1f);
if (Mathf.Abs(newDensity - generationSettings.density) > 0.001f)
{
    SaveParameterState(); // 保存当前状态
    generationSettings.density = newDensity; // 应用新值
}
```

## 🔍 预制体生成Undo流程

### 生成阶段
```
1. 清除上次生成记录
2. 设置Unity Undo组
3. 逐个生成预制体：
   - 实例化预制体
   - 注册到Undo系统
   - 记录到本地列表
4. 合并Undo操作
5. 显示完成对话框
```

### 撤销方式

#### 方式1: Unity编辑器Undo (推荐)
- **快捷键**: Ctrl+Z
- **菜单**: Edit → Undo
- **优势**: 与Unity完全集成，支持复杂撤销链

#### 方式2: 工具内清除按钮
- **按钮**: "清除生成"
- **功能**: 快速删除最后一次生成的所有预制体
- **优势**: 专门针对生成操作，更直观

## 💡 使用场景

### 场景1: 参数调优
```
用户操作：
1. 调整密度滑块到0.8
2. 发现效果不理想
3. 点击"撤销参数"回到0.5
4. 继续调整到0.6

结果：快速找到最佳参数
```

### 场景2: 生成实验
```
用户操作：
1. 生成一批预制体
2. 发现位置不合适
3. 按Ctrl+Z撤销生成
4. 调整参数重新生成

结果：无损实验不同方案
```

### 场景3: 复杂调整
```
用户操作：
1. 调整多个参数
2. 生成预制体查看效果
3. 使用"撤销参数"回退多步
4. 尝试不同的参数组合

结果：支持复杂的参数探索
```

### 场景4: 意外操作恢复
```
用户操作：
1. 意外改变了重要参数
2. 使用"撤销参数"快速恢复
3. 或者意外生成了预制体
4. 使用Ctrl+Z或"清除生成"恢复

结果：防止意外操作造成损失
```

## 🎉 功能优势

### 1. 双重保护
- ✅ **参数级别**: 保护设置不被意外更改
- ✅ **对象级别**: 保护场景不被意外生成污染

### 2. 无缝集成
- ✅ **Unity Undo**: 与编辑器原生Undo系统完全集成
- ✅ **标准快捷键**: 支持Ctrl+Z等标准操作

### 3. 智能管理
- ✅ **历史限制**: 自动管理内存，防止历史过多
- ✅ **状态独立**: 深拷贝确保历史状态不相互影响

### 4. 用户友好
- ✅ **即时反馈**: 参数更改立即保存历史
- ✅ **视觉提示**: 按钮状态清楚显示可用操作

## 🔧 技术细节

### 深拷贝机制
```csharp
private static GenerationSettings DeepCopyGenerationSettings(GenerationSettings original)
{
    GenerationSettings copy = new GenerationSettings();
    copy.generationMode = original.generationMode;
    copy.densityMode = original.densityMode;
    copy.density = original.density;
    // ... 复制所有字段
    return copy;
}
```

### 历史记录管理
```csharp
// 分支处理：新操作删除后续历史
if (currentParameterIndex < parameterHistory.Count - 1)
{
    parameterHistory.RemoveRange(currentParameterIndex + 1, 
        parameterHistory.Count - currentParameterIndex - 1);
}

// 容量限制：超出时删除最旧记录
if (parameterHistory.Count > MAX_PARAMETER_UNDO_STEPS)
{
    parameterHistory.RemoveAt(0);
    currentParameterIndex--;
}
```

### Unity Undo最佳实践
```csharp
// 1. 创建Undo组
Undo.IncrementCurrentGroup();
Undo.SetCurrentGroupName("Generate Prefabs");

// 2. 注册每个对象
Undo.RegisterCreatedObjectUndo(instance, "Generate Prefab");

// 3. 合并操作
Undo.CollapseUndoOperations(undoGroupIndex);
```

## 🎯 性能考虑

### 内存管理
- **参数历史**: 最多30步，自动清理
- **对象引用**: 只保存最后一次生成的引用
- **深拷贝**: 只在必要时进行，避免过度复制

### 响应性
- **即时保存**: 参数更改立即保存，无延迟
- **批量操作**: Unity Undo操作合并，减少历史条目
- **智能检测**: 只在值真正改变时保存状态

现在预制体生成工具具有完整的双重Undo系统，既保护参数设置又保护生成结果，大大提升了用户体验和工作效率！
