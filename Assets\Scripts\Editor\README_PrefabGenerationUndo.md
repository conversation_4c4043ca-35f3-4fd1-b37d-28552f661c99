# 预制体生成Undo系统

## 🔧 功能概述

为预制体生成工具添加了完整的Unity Undo集成，所有参数更改和预制体生成都使用Unity原生的Ctrl+Z撤销系统。

## 🎯 统一Undo系统

### 1. 参数Undo系统 ⚙️
**功能**: 使用Unity原生Undo系统撤销所有参数更改
**快捷键**: Ctrl+Z / Ctrl+Y
**范围**:
- 生成模式选择
- 密度滑块
- 最小间距设置
- 父物体设置
- 对齐地面选项
- 地面层设置
- 随机种子选项
- 预览大小滑块
- 预览列数滑块
- 高质量预览选项
- 包含子文件夹选项

### 2. 预制体生成Undo系统 🏗️
**功能**: 使用Unity内置Undo系统撤销生成的预制体
**快捷键**: Ctrl+Z / Ctrl+Y
**范围**:
- 生成的所有预制体实例
- 与Unity编辑器完全集成
- 支持Unity的Undo历史面板

## 🎨 UI界面

### 简化的控制按钮
```
                    [清除生成]
```

- **清除生成**: 删除最后一次生成的所有预制体
- **Ctrl+Z**: 撤销任何参数更改或预制体生成
- **Ctrl+Y**: 重做任何操作

### 智能按钮状态
- **清除生成按钮**: 只有在有生成记录时才启用
- **Unity Undo**: 完全集成到Unity的撤销系统中

## 🔧 技术实现

### 设置数据结构
```csharp
[System.Serializable]
public class PrefabGenerationSettings : ScriptableObject
{
    public GenerationSettings generationSettings = new GenerationSettings();
    public int previewSize = 64;
    public int previewColumns = 4;
    public bool useHighQualityPreviews = true;
    public bool includeSubfolders = true;
}
```

### Unity Undo集成
```csharp
// 参数更改时的Undo记录
EditorGUI.BeginChangeCheck();
float newDensity = EditorGUILayout.Slider(settings.generationSettings.density, 0.01f, 1f);
if (EditorGUI.EndChangeCheck())
{
    Undo.RecordObject(settings, "Change Density");
    settings.generationSettings.density = newDensity;
}
```

### Unity Undo集成
```csharp
// 生成开始时设置Undo组
Undo.IncrementCurrentGroup();
Undo.SetCurrentGroupName("Generate Prefabs");

// 每个预制体注册到Undo系统
Undo.RegisterCreatedObjectUndo(instance, "Generate Prefab");

// 生成完成后合并操作
Undo.CollapseUndoOperations(undoGroupIndex);
```

## 🎯 Unity Undo触发时机

### 智能检测策略
```
参数类型          | 触发时机           | Unity Undo行为
----------------|------------------|------------------
枚举选择         | 值改变时           | 记录对象状态变化
滑块拖动         | 值改变时           | 记录对象状态变化
数值输入         | 值改变时           | 记录对象状态变化
布尔切换         | 状态改变时         | 记录对象状态变化
对象引用         | 引用改变时         | 记录对象状态变化
```

### Unity变化检测机制
```csharp
// 示例：密度滑块的Unity Undo集成
EditorGUI.BeginChangeCheck();
float newDensity = EditorGUILayout.Slider(settings.generationSettings.density, 0.01f, 1f);
if (EditorGUI.EndChangeCheck())
{
    Undo.RecordObject(settings, "Change Density"); // Unity Undo记录
    settings.generationSettings.density = newDensity; // 应用新值
}
```

## 🔍 预制体生成Undo流程

### 生成阶段
```
1. 清除上次生成记录
2. 设置Unity Undo组
3. 逐个生成预制体：
   - 实例化预制体
   - 注册到Undo系统
   - 记录到本地列表
4. 合并Undo操作
5. 显示完成对话框
```

### 撤销方式

#### 方式1: Unity编辑器Undo (主要方式)
- **快捷键**: Ctrl+Z / Ctrl+Y
- **菜单**: Edit → Undo / Edit → Redo
- **范围**: 所有参数更改和预制体生成
- **优势**: 与Unity完全集成，支持复杂撤销链

#### 方式2: 工具内清除按钮 (辅助方式)
- **按钮**: "清除生成"
- **功能**: 快速删除最后一次生成的所有预制体
- **优势**: 专门针对生成操作，更直观

## 💡 使用场景

### 场景1: 参数调优
```
用户操作：
1. 调整密度滑块到0.8
2. 发现效果不理想
3. 按Ctrl+Z回到0.5
4. 继续调整到0.6

结果：快速找到最佳参数
```

### 场景2: 生成实验
```
用户操作：
1. 生成一批预制体
2. 发现位置不合适
3. 按Ctrl+Z撤销生成
4. 调整参数重新生成

结果：无损实验不同方案
```

### 场景3: 复杂调整
```
用户操作：
1. 调整多个参数
2. 生成预制体查看效果
3. 按Ctrl+Z多次回退参数
4. 尝试不同的参数组合

结果：支持复杂的参数探索
```

### 场景4: 意外操作恢复
```
用户操作：
1. 意外改变了重要参数
2. 按Ctrl+Z快速恢复
3. 或者意外生成了预制体
4. 使用Ctrl+Z或"清除生成"恢复

结果：防止意外操作造成损失
```

## 🎉 功能优势

### 1. 统一体验
- ✅ **参数级别**: 所有设置更改都使用Unity Undo
- ✅ **对象级别**: 预制体生成使用Unity Undo
- ✅ **一致性**: 整个工具使用统一的撤销体验

### 2. 原生集成
- ✅ **Unity Undo**: 与编辑器原生Undo系统完全集成
- ✅ **标准快捷键**: 支持Ctrl+Z/Ctrl+Y等标准操作
- ✅ **Undo历史**: 支持Unity的Undo历史面板

### 3. 智能检测
- ✅ **EditorGUI.BeginChangeCheck**: 使用Unity标准变化检测
- ✅ **自动记录**: 只在值真正改变时记录Undo
- ✅ **ScriptableObject**: 使用Unity序列化系统

### 4. 用户友好
- ✅ **即时响应**: 参数更改立即记录到Unity Undo
- ✅ **简化UI**: 移除冗余按钮，依赖标准快捷键
- ✅ **清除功能**: 保留专用清除按钮作为快捷方式

## 🔧 技术细节

### ScriptableObject序列化
```csharp
[System.Serializable]
public class PrefabGenerationSettings : ScriptableObject
{
    // Unity自动处理序列化和反序列化
    public GenerationSettings generationSettings = new GenerationSettings();
    public int previewSize = 64;
    // ... 其他字段
}
```

### Unity Undo最佳实践
```csharp
// 1. 使用EditorGUI.BeginChangeCheck检测变化
EditorGUI.BeginChangeCheck();
float newValue = EditorGUILayout.Slider(currentValue, 0f, 1f);
if (EditorGUI.EndChangeCheck())
{
    // 2. 记录对象状态
    Undo.RecordObject(settings, "Change Value");
    // 3. 应用新值
    currentValue = newValue;
}
```

### Unity Undo最佳实践
```csharp
// 1. 创建Undo组
Undo.IncrementCurrentGroup();
Undo.SetCurrentGroupName("Generate Prefabs");

// 2. 注册每个对象
Undo.RegisterCreatedObjectUndo(instance, "Generate Prefab");

// 3. 合并操作
Undo.CollapseUndoOperations(undoGroupIndex);
```

## 🎯 性能考虑

### 内存管理
- **Unity管理**: Unity自动管理Undo历史，无需手动清理
- **ScriptableObject**: 使用Unity序列化，内存效率高
- **对象引用**: 只保存最后一次生成的引用用于快速清除

### 响应性
- **即时记录**: 参数更改立即记录到Unity Undo，无延迟
- **批量操作**: 预制体生成使用Undo组，减少历史条目
- **智能检测**: EditorGUI.BeginChangeCheck确保只在真正改变时记录

### Unity集成优势
- **原生性能**: 使用Unity原生Undo系统，性能最优
- **自动优化**: Unity自动优化Undo历史存储和访问
- **内存安全**: Unity自动处理对象生命周期和内存回收

现在预制体生成工具具有完整的Unity原生Undo系统，所有操作都可以用Ctrl+Z撤销，提供了最佳的用户体验和性能！
