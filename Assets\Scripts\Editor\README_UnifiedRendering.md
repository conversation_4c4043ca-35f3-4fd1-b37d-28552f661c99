# 区域选择统一渲染系统

## 🔧 问题解决

**原问题**: 网格的渲染逻辑还是不一样，需要清理统一

**解决方案**: 创建统一的渲染方法，确保所有UI元素使用完全一致的渲染逻辑

## 🎯 统一渲染架构

### 核心渲染方法

#### 1. 统一UI元素渲染
```csharp
private void DrawUIElement(Vector3[] corners, Color fillColor, Color outlineColor, float heightOffset)
{
    // 设置统一的深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
    
    // 应用高度偏移
    for (int i = 0; i < corners.Length; i++)
    {
        corners[i] = new Vector3(corners[i].x, corners[i].y + heightOffset, corners[i].z);
    }
    
    // 绘制UI元素
    Handles.DrawSolidRectangleWithOutline(corners, fillColor, outlineColor);
    
    // 恢复默认深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
}
```

#### 2. 统一圆点标记渲染
```csharp
private void DrawUIMarker(Vector3 center, Color color, float radius, float heightOffset)
{
    // 设置统一的深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
    
    // 应用高度偏移
    center = new Vector3(center.x, center.y + heightOffset, center.z);
    
    // 绘制圆点标记
    Handles.DrawSolidDisc(center, Vector3.up, radius);
    
    // 恢复默认深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
}
```

## 🎨 统一的层级系统

### 高度偏移层级
```
层级从上到下:
1. 悬停指示器 (0.03f):
   - 🟢 绿色画笔指示器
   - 🔴 红色擦除指示器

2. 起始点和矩形预览 (0.02f):
   - 🟡 黄色矩形选择起始点
   - 🟡 黄色矩形选择预览
   - 🔴 红色擦除起始点
   - 🔴 红色擦除预览

3. 选中区域 (0.01f):
   - 🟢 绿色选中区域填充
```

### 深度测试统一
所有UI元素都使用相同的深度测试：
- **设置**: `CompareFunction.LessEqual`
- **恢复**: `CompareFunction.Always`

## 🔧 重构的UI元素

### 1. 绿色选中区域 🟢
**修复前**:
```csharp
// 手动深度测试设置
Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
// 直接绘制
Handles.DrawSolidRectangleWithOutline(corners, selectedColor, Color.clear);
// 手动恢复深度测试
Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
```

**修复后**:
```csharp
// 使用统一渲染方法
DrawUIElement(corners, selectedColor, Color.clear, 0.01f);
```

### 2. 黄色矩形选择 🟡
**起始点标记**:
```csharp
// 修复前: 手动深度测试和绘制
// 修复后: 统一标记渲染
DrawUIMarker(startCenter, Color.yellow, GRID_SIZE * 0.25f, 0.02f);
```

**矩形预览**:
```csharp
// 修复前: 手动深度测试和绘制
// 修复后: 统一元素渲染
DrawUIElement(corners, new Color(1f, 1f, 0f, 0.2f), Color.yellow, 0.02f);
```

### 3. 红色擦除功能 🔴
**起始点标记**:
```csharp
// 使用统一标记渲染
DrawUIMarker(startCenter, Color.red, GRID_SIZE * 0.25f, 0.02f);
```

**矩形预览**:
```csharp
// 使用统一元素渲染
DrawUIElement(corners, new Color(1f, 0f, 0f, 0.2f), Color.red, 0.02f);
```

**悬停指示器**:
```csharp
// 使用统一元素渲染
DrawUIElement(corners, new Color(1f, 0.2f, 0.2f, 0.3f), Color.red, 0.03f);
```

### 4. 绿色画笔指示器 🟢
```csharp
// 使用统一元素渲染
DrawUIElement(corners, new Color(0.2f, 0.8f, 0.2f, 0.3f), Color.white, 0.03f);
```

## 🎯 统一渲染的优势

### 1. 代码一致性
- ✅ **统一接口**: 所有UI元素使用相同的渲染方法
- ✅ **参数标准化**: 统一的参数传递方式
- ✅ **错误减少**: 避免手动深度测试设置错误

### 2. 维护性提升
- ✅ **集中管理**: 深度测试逻辑集中在统一方法中
- ✅ **易于修改**: 修改渲染逻辑只需改动统一方法
- ✅ **调试简化**: 统一的渲染流程便于调试

### 3. 性能优化
- ✅ **减少重复**: 避免重复的深度测试设置代码
- ✅ **统一优化**: 可以在统一方法中进行性能优化
- ✅ **内存效率**: 减少代码重复，提高内存效率

### 4. 视觉一致性
- ✅ **层级统一**: 所有元素遵循相同的层级规则
- ✅ **深度一致**: 统一的深度测试确保一致的视觉效果
- ✅ **偏移标准**: 标准化的高度偏移避免视觉冲突

## 🔍 渲染流程

### 统一渲染流程
```
1. 准备顶点数据 (地面高度检测)
   ↓
2. 调用统一渲染方法
   ↓
3. 设置深度测试 (LessEqual)
   ↓
4. 应用高度偏移
   ↓
5. 执行绘制操作
   ↓
6. 恢复深度测试 (Always)
```

### 地面检测集成
```csharp
// 所有元素都使用相同的地面检测
float groundY = GetGroundHeight(x, z);

// 创建基础顶点（地面高度）
Vector3[] corners = new Vector3[]
{
    new Vector3(x1, groundY, z1),
    new Vector3(x2, groundY, z2),
    // ...
};

// 统一渲染方法自动应用偏移
DrawUIElement(corners, fillColor, outlineColor, heightOffset);
```

## 💡 使用指南

### 添加新UI元素
```csharp
// 1. 获取地面高度
float groundY = GetGroundHeight(x, z);

// 2. 创建基础顶点
Vector3[] corners = new Vector3[] { /* 基础顶点 */ };

// 3. 选择合适的层级偏移
float heightOffset = 0.01f; // 或 0.02f, 0.03f

// 4. 使用统一渲染方法
DrawUIElement(corners, fillColor, outlineColor, heightOffset);
```

### 层级选择指南
- **0.01f**: 基础选中区域、边框
- **0.02f**: 起始点标记、矩形预览
- **0.03f**: 悬停指示器、画笔预览

## 🎉 最终效果

### 代码质量提升
- ✅ **重复代码消除**: 从多处重复的深度测试代码变为统一方法
- ✅ **接口标准化**: 所有UI渲染使用标准化接口
- ✅ **错误率降低**: 统一的渲染逻辑减少人为错误

### 视觉效果统一
- ✅ **层级清晰**: 明确的三层高度偏移系统
- ✅ **深度一致**: 所有元素使用相同的深度测试逻辑
- ✅ **渲染稳定**: 统一的渲染流程确保稳定的视觉效果

### 开发体验改善
- ✅ **代码简洁**: 渲染代码从多行减少到单行调用
- ✅ **维护轻松**: 修改渲染逻辑只需修改统一方法
- ✅ **扩展容易**: 添加新UI元素只需调用统一方法

现在所有的网格渲染都使用完全统一的逻辑，确保了一致的视觉效果和代码质量！
