using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// 区域选择器工具类，提供鼠标事件处理和绘制功能
/// </summary>
public static class AreaSelectorUtils
{
    // 网格大小（1米）
    private const float GRID_SIZE = 1f;

    // 处理鼠标事件
    public static void HandleMouseEvents(Event e, SceneView sceneView, ref Vector2Int hoveredCell, ref Vector2Int lastDrawCell,
        ref HashSet<Vector2Int> selectedCells, ref Vector2Int rectangleStart, ref bool isSelectingRectangle, ref bool waitingForSecondClick,
        ref Vector2Int eraseStart, ref bool isSelectingEraseRect, ref bool waitingForSecondEraseClick,
        AreaSelectorTab.SelectionMode currentMode, int brushSize, bool limitEditRange, Vector2Int editRangeOrigin, int editRangeWidth, int editRangeHeight)
    {
        // 右键点击取消当前操作
        if (e.type == EventType.MouseDown && e.button == 1)
        {
            if (currentMode == AreaSelectorTab.SelectionMode.Point && isSelectingRectangle && waitingForSecondClick)
            {
                waitingForSecondClick = false;
                isSelectingRectangle = false;
                e.Use();
            }
            else if (currentMode == AreaSelectorTab.SelectionMode.Erase && waitingForSecondEraseClick)
            {
                waitingForSecondEraseClick = false;
                isSelectingEraseRect = false;
                e.Use();
            }
        }

        // 鼠标释放事件
        if (e.type == EventType.MouseUp && e.button == 0)
        {
            // 在绘制模式下，重置上一个绘制点
            if (currentMode == AreaSelectorTab.SelectionMode.Draw)
            {
                // 重置上一个绘制点，下次开始新的绘制
                lastDrawCell = Vector2Int.zero;
                e.Use();
            }
        }

        // 如果限制编辑范围，检查当前单元格是否在范围内
        if (limitEditRange)
        {
            if (hoveredCell.x < editRangeOrigin.x || hoveredCell.x >= editRangeOrigin.x + editRangeWidth ||
                hoveredCell.y < editRangeOrigin.y || hoveredCell.y >= editRangeOrigin.y + editRangeHeight)
            {
                return; // 不在编辑范围内，不处理鼠标事件
            }
        }

        // 鼠标左键点击
        if (e.type == EventType.MouseDown && e.button == 0)
        {
            switch (currentMode)
            {
                case AreaSelectorTab.SelectionMode.Point:
                    if (e.shift) // 按住Shift键进行矩形选择
                    {
                        if (waitingForSecondClick)
                        {
                            // 第二次点击，完成矩形选择
                            // 计算矩形区域并添加到选中区域
                            int minX = Mathf.Min(rectangleStart.x, hoveredCell.x);
                            int maxX = Mathf.Max(rectangleStart.x, hoveredCell.x);
                            int minY = Mathf.Min(rectangleStart.y, hoveredCell.y);
                            int maxY = Mathf.Max(rectangleStart.y, hoveredCell.y);

                            // 如果限制编辑范围，限制矩形范围
                            if (limitEditRange)
                            {
                                minX = Mathf.Max(minX, editRangeOrigin.x);
                                maxX = Mathf.Min(maxX, editRangeOrigin.x + editRangeWidth);
                                minY = Mathf.Max(minY, editRangeOrigin.y);
                                maxY = Mathf.Min(maxY, editRangeOrigin.y + editRangeHeight);
                            }

                            for (int x = minX; x <= maxX; x++)
                            {
                                for (int y = minY; y <= maxY; y++)
                                {
                                    selectedCells.Add(new Vector2Int(x, y));
                                }
                            }

                            waitingForSecondClick = false;
                            isSelectingRectangle = false;
                        }
                        else
                        {
                            // 第一次点击，记录起始点
                            rectangleStart = hoveredCell;
                            waitingForSecondClick = true;
                            isSelectingRectangle = true;
                        }
                    }
                    else // 普通点选
                    {
                        selectedCells.Add(hoveredCell);
                    }
                    e.Use();
                    break;

                case AreaSelectorTab.SelectionMode.Draw:
                    // 添加当前单元格到选中区域
                    AddCellsInBrushRange(hoveredCell, ref selectedCells, brushSize, limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight);
                    // 记录起始绘制点
                    lastDrawCell = hoveredCell;
                    // 强制重绘场景视图，立即显示绘制效果
                    SceneView.RepaintAll();
                    e.Use();
                    break;

                case AreaSelectorTab.SelectionMode.Erase:
                    if (e.shift) // 按住Shift键进行矩形擦除
                    {
                        if (waitingForSecondEraseClick)
                        {
                            // 第二次点击，完成矩形擦除
                            // 计算矩形区域并从选中区域中移除
                            int minX = Mathf.Min(eraseStart.x, hoveredCell.x);
                            int maxX = Mathf.Max(eraseStart.x, hoveredCell.x);
                            int minY = Mathf.Min(eraseStart.y, hoveredCell.y);
                            int maxY = Mathf.Max(eraseStart.y, hoveredCell.y);

                            // 如果限制编辑范围，限制矩形范围
                            if (limitEditRange)
                            {
                                minX = Mathf.Max(minX, editRangeOrigin.x);
                                maxX = Mathf.Min(maxX, editRangeOrigin.x + editRangeWidth);
                                minY = Mathf.Max(minY, editRangeOrigin.y);
                                maxY = Mathf.Min(maxY, editRangeOrigin.y + editRangeHeight);
                            }

                            for (int x = minX; x <= maxX; x++)
                            {
                                for (int y = minY; y <= maxY; y++)
                                {
                                    selectedCells.Remove(new Vector2Int(x, y));
                                }
                            }

                            waitingForSecondEraseClick = false;
                            isSelectingEraseRect = false;
                        }
                        else
                        {
                            // 第一次点击，记录起始点
                            eraseStart = hoveredCell;
                            waitingForSecondEraseClick = true;
                            isSelectingEraseRect = true;
                        }
                    }
                    else // 普通擦除
                    {
                        selectedCells.Remove(hoveredCell);
                    }
                    e.Use();
                    break;
            }
        }

        // 鼠标拖动
        if (e.type == EventType.MouseDrag && e.button == 0)
        {
            switch (currentMode)
            {
                case AreaSelectorTab.SelectionMode.Point:
                    if (waitingForSecondClick && isSelectingRectangle)
                    {
                        // 如果是矩形选择模式，更新预览
                        SceneView.RepaintAll();
                        e.Use();
                    }
                    else
                    {
                        // 普通点选拖动
                        selectedCells.Add(hoveredCell);
                        e.Use();
                    }
                    break;

                case AreaSelectorTab.SelectionMode.Draw:
                    // 绘制模式下的拖动，添加当前单元格和上一个单元格之间的所有单元格，形成连续的线条
                    AddCellsInBrushRange(hoveredCell, ref selectedCells, brushSize, limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight);

                    // 如果有上一个绘制点，则连接两点之间的所有单元格
                    if (lastDrawCell != hoveredCell)
                    {
                        // 使用Bresenham算法连接两点之间的所有单元格，并应用画笔大小
                        ConnectCellsWithBrush(lastDrawCell, hoveredCell, ref selectedCells, brushSize, limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight);
                        lastDrawCell = hoveredCell;
                    }

                    // 强制重绘场景视图，立即显示绘制效果
                    SceneView.RepaintAll();
                    e.Use();
                    break;

                case AreaSelectorTab.SelectionMode.Erase:
                    if (waitingForSecondEraseClick && isSelectingEraseRect)
                    {
                        // 如果是矩形擦除模式，更新预览
                        SceneView.RepaintAll();
                        e.Use();
                    }
                    else
                    {
                        // 普通擦除拖动
                        selectedCells.Remove(hoveredCell);
                        e.Use();
                    }
                    break;
            }
        }
    }

    // 在画笔范围内添加单元格
    public static void AddCellsInBrushRange(Vector2Int center, ref HashSet<Vector2Int> selectedCells, int brushSize,
        bool limitEditRange, Vector2Int editRangeOrigin, int editRangeWidth, int editRangeHeight)
    {
        // 计算画笔范围的起始点和结束点
        int halfSize = brushSize / 2;
        int startX, startY, endX, endY;

        // 根据画笔大小是奇数还是偶数，计算不同的起始点和结束点
        if (brushSize % 2 == 1) // 奇数大小 (1x1, 3x3, 5x5)
        {
            startX = center.x - halfSize;
            startY = center.y - halfSize;
            endX = center.x + halfSize;
            endY = center.y + halfSize;
        }
        else // 偶数大小 (2x2, 4x4)
        {
            startX = center.x - halfSize + 1;
            startY = center.y - halfSize + 1;
            endX = center.x + halfSize;
            endY = center.y + halfSize;
        }

        // 遍历画笔范围内的所有单元格
        for (int x = startX; x <= endX; x++)
        {
            for (int y = startY; y <= endY; y++)
            {
                Vector2Int cell = new Vector2Int(x, y);

                // 如果限制编辑范围，检查单元格是否在范围内
                if (limitEditRange)
                {
                    if (x < editRangeOrigin.x || x >= editRangeOrigin.x + editRangeWidth ||
                        y < editRangeOrigin.y || y >= editRangeOrigin.y + editRangeHeight)
                    {
                        continue;
                    }
                }

                // 添加单元格到选中区域
                selectedCells.Add(cell);
            }
        }
    }

    // 使用画笔连接两个单元格之间的所有单元格
    public static void ConnectCellsWithBrush(Vector2Int start, Vector2Int end, ref HashSet<Vector2Int> selectedCells, int brushSize,
        bool limitEditRange, Vector2Int editRangeOrigin, int editRangeWidth, int editRangeHeight)
    {
        // 使用Bresenham算法连接两点
        List<Vector2Int> pathCells = GetLineCells(start, end);

        // 对路径上的每个单元格应用画笔
        foreach (Vector2Int cell in pathCells)
        {
            AddCellsInBrushRange(cell, ref selectedCells, brushSize, limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight);
        }
    }

    // 连接两个单元格之间的所有单元格（使用Bresenham算法）
    public static List<Vector2Int> GetLineCells(Vector2Int start, Vector2Int end)
    {
        List<Vector2Int> cells = new List<Vector2Int>();

        int x0 = start.x;
        int y0 = start.y;
        int x1 = end.x;
        int y1 = end.y;

        int dx = Mathf.Abs(x1 - x0);
        int dy = Mathf.Abs(y1 - y0);
        int sx = x0 < x1 ? 1 : -1;
        int sy = y0 < y1 ? 1 : -1;
        int err = dx - dy;

        while (true)
        {
            // 添加当前单元格
            cells.Add(new Vector2Int(x0, y0));

            // 如果到达终点，退出循环
            if (x0 == x1 && y0 == y1) break;

            int e2 = 2 * err;
            if (e2 > -dy)
            {
                err -= dy;
                x0 += sx;
            }
            if (e2 < dx)
            {
                err += dx;
                y0 += sy;
            }
        }

        return cells;
    }

    // 绘制网格
    public static void DrawGrid(SceneView sceneView, bool showGrid, Color gridColor, Color rangeColor,
        bool limitEditRange, Vector2Int editRangeOrigin, int editRangeWidth, int editRangeHeight)
    {
        // 检查场景视图和相机是否有效
        if (sceneView == null || sceneView.camera == null)
        {
            return;
        }

        // 如果不显示网格，直接返回
        if (!showGrid) return;

        // 获取相机视锥体的边界
        Camera camera = sceneView.camera;
        float distance = 10f; // 假设网格在y=0平面上
        Vector3 bottomLeft = camera.ViewportToWorldPoint(new Vector3(0, 0, distance));
        Vector3 topRight = camera.ViewportToWorldPoint(new Vector3(1, 1, distance));

        // 计算网格范围
        int startX, startZ, endX, endZ;

        if (limitEditRange)
        {
            // 使用编辑范围限制网格范围
            startX = editRangeOrigin.x;
            startZ = editRangeOrigin.y;
            endX = editRangeOrigin.x + editRangeWidth;
            endZ = editRangeOrigin.y + editRangeHeight;
        }
        else
        {
            // 使用相机视锥体范围
            startX = Mathf.FloorToInt(bottomLeft.x / GRID_SIZE) - 5;
            startZ = Mathf.FloorToInt(bottomLeft.z / GRID_SIZE) - 5;
            endX = Mathf.CeilToInt(topRight.x / GRID_SIZE) + 5;
            endZ = Mathf.CeilToInt(topRight.z / GRID_SIZE) + 5;

            // 限制网格范围，避免绘制过多
            startX = Mathf.Max(startX, -1000);
            startZ = Mathf.Max(startZ, -1000);
            endX = Mathf.Min(endX, 1000);
            endZ = Mathf.Min(endZ, 1000);
        }

        // 设置深度测试，确保网格绘制在地面上
        Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

        // 绘制网格线
        Handles.color = gridColor;

        // 绘制水平线
        for (int z = startZ; z <= endZ; z++)
        {
            float startY = GetGroundHeight(startX * GRID_SIZE, z * GRID_SIZE);
            float endY = GetGroundHeight(endX * GRID_SIZE, z * GRID_SIZE);
            Vector3 start = new Vector3(startX * GRID_SIZE, startY, z * GRID_SIZE);
            Vector3 end = new Vector3(endX * GRID_SIZE, endY, z * GRID_SIZE);
            Handles.DrawLine(start, end);
        }

        // 绘制垂直线
        for (int x = startX; x <= endX; x++)
        {
            float startY = GetGroundHeight(x * GRID_SIZE, startZ * GRID_SIZE);
            float endY = GetGroundHeight(x * GRID_SIZE, endZ * GRID_SIZE);
            Vector3 start = new Vector3(x * GRID_SIZE, startY, startZ * GRID_SIZE);
            Vector3 end = new Vector3(x * GRID_SIZE, endY, endZ * GRID_SIZE);
            Handles.DrawLine(start, end);
        }

        // 恢复默认深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;

        // 如果限制编辑范围，绘制编辑范围边界
        if (limitEditRange)
        {
            Handles.color = rangeColor;

            // 获取四个角的地面高度
            float cornerY1 = GetGroundHeight(startX * GRID_SIZE, startZ * GRID_SIZE);
            float cornerY2 = GetGroundHeight(endX * GRID_SIZE, startZ * GRID_SIZE);
            float cornerY3 = GetGroundHeight(endX * GRID_SIZE, endZ * GRID_SIZE);
            float cornerY4 = GetGroundHeight(startX * GRID_SIZE, endZ * GRID_SIZE);

            Vector3[] corners = new Vector3[]
            {
                new Vector3(startX * GRID_SIZE, cornerY1, startZ * GRID_SIZE),
                new Vector3(endX * GRID_SIZE, cornerY2, startZ * GRID_SIZE),
                new Vector3(endX * GRID_SIZE, cornerY3, endZ * GRID_SIZE),
                new Vector3(startX * GRID_SIZE, cornerY4, endZ * GRID_SIZE)
            };

            Handles.DrawSolidRectangleWithOutline(corners, new Color(rangeColor.r, rangeColor.g, rangeColor.b, 0.1f), rangeColor);
        }
    }

    // 只绘制编辑范围边界（用于预览模式）
    public static void DrawEditRangeBorder(Color rangeColor, Vector2Int editRangeOrigin, int editRangeWidth, int editRangeHeight)
    {
        int startX = editRangeOrigin.x;
        int startZ = editRangeOrigin.y;
        int endX = editRangeOrigin.x + editRangeWidth;
        int endZ = editRangeOrigin.y + editRangeHeight;

        // 设置深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
        Handles.color = rangeColor;

        // 获取四个角的地面高度
        float cornerY1 = GetGroundHeight(startX * GRID_SIZE, startZ * GRID_SIZE);
        float cornerY2 = GetGroundHeight(endX * GRID_SIZE, startZ * GRID_SIZE);
        float cornerY3 = GetGroundHeight(endX * GRID_SIZE, endZ * GRID_SIZE);
        float cornerY4 = GetGroundHeight(startX * GRID_SIZE, endZ * GRID_SIZE);

        Vector3[] corners = new Vector3[]
        {
            new Vector3(startX * GRID_SIZE, cornerY1 + 0.01f, startZ * GRID_SIZE),
            new Vector3(endX * GRID_SIZE, cornerY2 + 0.01f, startZ * GRID_SIZE),
            new Vector3(endX * GRID_SIZE, cornerY3 + 0.01f, endZ * GRID_SIZE),
            new Vector3(startX * GRID_SIZE, cornerY4 + 0.01f, endZ * GRID_SIZE)
        };

        // 只绘制边框，不绘制填充
        Handles.DrawPolyLine(corners[0], corners[1], corners[2], corners[3], corners[0]);

        // 恢复默认深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
    }

    // 获取地面高度
    private static float GetGroundHeight(float x, float z)
    {
        // 从上方向下发射射线检测地面
        Vector3 rayStart = new Vector3(x, 1000f, z);
        Vector3 rayDirection = Vector3.down;

        if (Physics.Raycast(rayStart, rayDirection, out RaycastHit hit, 2000f))
        {
            return hit.point.y;
        }

        // 如果没有检测到地面，返回0
        return 0f;
    }
}
