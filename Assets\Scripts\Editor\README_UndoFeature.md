# 撤销和清除功能

## 🔄 功能概述

为了解决生成后可能出现的不理想情况，工具现在提供了多种清除和撤销选项，让你可以快速调整生成结果。

## 🎯 三种清除方式

### 1. 撤销上次生成 🔄
**功能**: 精确删除最近一次生成的所有预制体
**使用场景**: 刚生成完发现效果不理想，想要重新生成

**特点**:
- ✅ 只删除最近一次生成的预制体
- ✅ 不影响之前生成的或手动放置的预制体
- ✅ 精确记录，不会误删
- ✅ 操作安全，有确认提示

**按钮颜色**: 🟠 橙色（温和的撤销操作）

### 2. 清除所有 🗑️
**功能**: 清除场景中所有预制体实例（谨慎使用）
**使用场景**: 想要完全重新开始，清空所有生成的预制体

**特点**:
- ⚠️ 删除场景中所有预制体实例
- ⚠️ 包括手动放置的预制体
- ⚠️ 操作不可逆，需要确认
- ⚠️ 影响范围大，请谨慎使用

**按钮颜色**: 🔴 红色（危险操作）

### 3. Unity内置撤销 ↩️
**功能**: 使用Unity的标准撤销系统（Ctrl+Z）
**使用场景**: 撤销最近的编辑器操作

**特点**:
- ✅ Unity标准功能
- ✅ 可以撤销多步操作
- ✅ 支持重做（Ctrl+Y）
- ✅ 适用于所有编辑器操作

## 🎨 界面设计

### 按钮布局
```
┌─────────────────────────────────────────────────────┐
│ [上一步：区域选择] [🟢生成预制体] [🟠撤销上次生成] [🔴清除所有] │
└─────────────────────────────────────────────────────┘
```

### 按钮状态
- **生成预制体**: 🟢 绿色，始终可用
- **撤销上次生成**: 🟠 橙色，只有在有上次生成记录时才显示
- **清除所有**: 🔴 红色，始终可用但有确认对话框

## 🔧 技术实现

### 生成记录机制
```csharp
// 记录生成的对象
private List<GameObject> lastGeneratedObjects = new List<GameObject>();
private bool hasLastGeneration = false;

// 生成时记录
lastGeneratedObjects.Add(instance);

// 生成完成后标记
hasLastGeneration = generatedCount > 0;
```

### 撤销实现
```csharp
public void UndoLastGeneration()
{
    // 删除所有记录的对象
    for (int i = lastGeneratedObjects.Count - 1; i >= 0; i--)
    {
        GameObject obj = lastGeneratedObjects[i];
        if (obj != null)
        {
            Object.DestroyImmediate(obj);
        }
    }
    
    // 清除记录
    lastGeneratedObjects.Clear();
    hasLastGeneration = false;
}
```

## 📋 使用流程

### 场景1: 生成效果不满意
```
1. 点击"生成预制体" → 生成了一些预制体
2. 发现密度太高或位置不理想
3. 点击"撤销上次生成" → 删除刚生成的预制体
4. 调整参数（密度、种子等）
5. 重新点击"生成预制体"
```

### 场景2: 多次生成后想要重新开始
```
1. 已经进行了多次生成操作
2. 想要完全重新开始
3. 点击"清除所有" → 确认删除
4. 场景恢复到初始状态
5. 重新配置参数并生成
```

### 场景3: 使用Unity标准撤销
```
1. 进行了生成操作
2. 按 Ctrl+Z → 撤销最近的操作
3. 可以连续按 Ctrl+Z 撤销多步
4. 按 Ctrl+Y 可以重做
```

## ⚠️ 注意事项

### 撤销上次生成
- ✅ **安全**: 只删除工具记录的对象
- ✅ **精确**: 不会误删其他预制体
- ❌ **限制**: 只能撤销最近一次生成
- ❌ **重启失效**: 关闭工具后记录会丢失

### 清除所有
- ⚠️ **危险**: 会删除场景中所有预制体实例
- ⚠️ **不可逆**: 操作无法撤销
- ⚠️ **影响广**: 包括手动放置的预制体
- ✅ **确认**: 有确认对话框防止误操作

### Unity撤销
- ✅ **标准**: Unity内置功能，稳定可靠
- ✅ **多步**: 可以撤销多个操作
- ❌ **通用**: 会撤销所有编辑器操作，不仅仅是预制体生成

## 🎯 最佳实践

### 推荐工作流程
1. **小批量测试**: 先在小区域测试生成效果
2. **及时撤销**: 发现问题立即使用"撤销上次生成"
3. **参数调整**: 根据效果调整密度、种子等参数
4. **逐步扩大**: 满意后再在大区域生成
5. **定期保存**: 满意的结果及时保存场景

### 避免的操作
- ❌ 不要在满意的结果上直接点击"清除所有"
- ❌ 不要在有重要手动预制体的场景中使用"清除所有"
- ❌ 不要依赖撤销功能进行大量试错，应该先调整参数

## 🔍 故障排除

### 撤销按钮不显示
**原因**: 没有上次生成的记录
**解决**: 先进行一次生成操作

### 撤销后仍有预制体
**原因**: 可能是之前生成的或手动放置的
**解决**: 使用"清除所有"或手动删除

### 清除所有没有效果
**原因**: 预制体可能不是通过工具生成的
**解决**: 手动选择并删除相关对象

## 🎉 功能优势

### 用户体验
- ✅ **快速纠错**: 立即撤销不满意的生成
- ✅ **安全操作**: 精确控制删除范围
- ✅ **灵活选择**: 多种清除方式适应不同需求
- ✅ **视觉反馈**: 按钮颜色清楚表示操作类型

### 工作效率
- ✅ **减少重复**: 不需要手动选择删除
- ✅ **快速迭代**: 生成→撤销→调整→重新生成
- ✅ **降低风险**: 避免误删重要对象
- ✅ **节省时间**: 一键操作替代繁琐的手动清理

现在你可以放心地进行预制体生成实验，因为随时可以快速撤销和重新开始！
