# 智能预制体生成算法

## 🔧 问题解决

**原问题**: 预制体生成时出现大量重叠，间隔和密度算法没有考虑预制体的实际占格数

**解决方案**: 重写生成算法，实现基于格数的智能冲突检测和位置计算

## 🎯 新算法特性

### 1. 格数感知生成
- 自动识别每个预制体的占格数（如3x3、2x4等）
- 生成时考虑预制体的实际占用空间
- 避免不同大小预制体之间的重叠

### 2. 智能冲突检测
- 检查预制体是否能完全放置在选中区域内
- 检测与已生成预制体的冲突
- 确保大型预制体不会超出选中边界

### 3. 占用网格管理
- 维护已占用格子的记录
- 动态更新占用状态
- 防止后续预制体放置在已占用位置

## 🔍 算法流程

### 1. 初始化阶段
```
1. 检查选中区域和预制体设置
2. 创建占用网格 (occupiedCells)
3. 随机打乱选中格子顺序
4. 初始化随机数生成器
```

### 2. 生成循环
```
对于每个选中的格子:
  1. 检查是否已被占用 → 跳过
  2. 应用密度过滤 → 可能跳过
  3. 随机选择预制体
  4. 检查是否能放置 → 不能则跳过
  5. 标记占用的所有格子
  6. 计算中心位置
  7. 实例化预制体
```

### 3. 位置计算
```
对于NxM格的预制体:
  - 起始位置: (startX, startY)
  - 中心位置: (startX + (N-1)*0.5, startY + (M-1)*0.5)
  - 占用范围: startX到startX+N-1, startY到startY+M-1
```

## 🧮 核心算法

### 冲突检测算法
```csharp
bool CanPlacePrefabAt(Vector2Int startCell, PrefabSetting prefabSetting, 
                     HashSet<Vector2Int> occupiedCells, HashSet<Vector2Int> selectedCells)
{
    Vector2Int gridSize = prefabSetting.gridSize;
    
    // 检查预制体占用的所有格子
    for (int x = 0; x < gridSize.x; x++)
    {
        for (int y = 0; y < gridSize.y; y++)
        {
            Vector2Int checkCell = startCell + new Vector2Int(x, y);
            
            // 检查是否在选中区域内
            if (!selectedCells.Contains(checkCell))
                return false;
            
            // 检查是否已被占用
            if (occupiedCells.Contains(checkCell))
                return false;
        }
    }
    
    return true;
}
```

### 占用标记算法
```csharp
void MarkOccupiedCells(Vector2Int startCell, PrefabSetting prefabSetting, 
                      HashSet<Vector2Int> occupiedCells)
{
    Vector2Int gridSize = prefabSetting.gridSize;
    
    for (int x = 0; x < gridSize.x; x++)
    {
        for (int y = 0; y < gridSize.y; y++)
        {
            Vector2Int occupiedCell = startCell + new Vector2Int(x, y);
            occupiedCells.Add(occupiedCell);
        }
    }
}
```

### 位置计算算法
```csharp
Vector3 CalculatePrefabPosition(Vector2Int startCell, PrefabSetting prefabSetting)
{
    Vector2Int gridSize = prefabSetting.gridSize;
    
    // 计算预制体的中心位置
    float centerX = startCell.x + (gridSize.x - 1) * 0.5f;
    float centerZ = startCell.y + (gridSize.y - 1) * 0.5f;
    
    return new Vector3(centerX, 0, centerZ);
}
```

## 📊 生成示例

### 场景1: 混合大小预制体
```
选中区域: 10x10格
预制体类型:
- 房屋 (3x3格)
- 树木 (1x1格)
- 道路 (2x4格)

生成结果:
┌─────────────────────────┐
│ T  [H H H]  T  T       │  T=树木(1x1)
│ T  [H H H]  T  T       │  H=房屋(3x3)
│ T  [H H H]  T  T       │  R=道路(2x4)
│                        │
│ [R R R R]  T  T        │
│ [R R R R]  T  T        │
│                        │
│ T  T  [H H H]  T       │
│ T  T  [H H H]  T       │
│ T  T  [H H H]  T       │
└─────────────────────────┘
```

### 场景2: 边界处理
```
大型预制体 (5x5) 在边界附近:
- 检查是否有足够空间
- 如果超出边界则跳过
- 确保完全在选中区域内
```

## 🎯 算法优势

### 1. 零重叠保证
- ✅ 完全消除预制体重叠问题
- ✅ 大型预制体正确占用多个格子
- ✅ 小型预制体填充剩余空间

### 2. 边界安全
- ✅ 大型预制体不会超出选中区域
- ✅ 自动跳过无法完全放置的位置
- ✅ 保持生成结果在预期范围内

### 3. 密度控制精确
- ✅ 密度参数仍然有效
- ✅ 大型预制体自然降低密度
- ✅ 小型预制体可以密集填充

### 4. 性能优化
- ✅ 使用HashSet快速查找占用状态
- ✅ 随机顺序避免模式化
- ✅ 早期跳过减少不必要计算

## 🔧 参数影响

### 密度参数 (Density)
- **高密度 (0.8-1.0)**: 尽可能填满空间，大型预制体较少
- **中密度 (0.4-0.7)**: 平衡的填充，留有空隙
- **低密度 (0.1-0.3)**: 稀疏分布，大量空白区域

### 预制体权重 (Weight)
- 影响预制体被选中的概率
- 高权重预制体更容易被生成
- 可以控制不同大小预制体的比例

## 📋 使用建议

### 1. 文件夹组织
```
建议按格数分类:
├── Small_1x1/          # 小型装饰物
├── Medium_2x2/         # 中型建筑
├── Large_3x3/          # 大型建筑
└── Roads_2x4/          # 特殊形状
```

### 2. 密度设置
```
- 混合大小: 密度 0.3-0.5
- 纯小型: 密度 0.6-0.8
- 纯大型: 密度 0.2-0.4
```

### 3. 区域选择
```
- 为大型预制体预留足够空间
- 考虑预制体的最大尺寸
- 避免过于狭窄的选中区域
```

## 🐛 故障排除

### 生成数量少于预期
**原因**: 大型预制体占用空间大，可用位置有限
**解决**: 
- 增加选中区域大小
- 降低密度参数
- 减少大型预制体比例

### 边缘区域空白
**原因**: 大型预制体无法在边缘完全放置
**解决**:
- 选择更大的区域
- 使用更多小型预制体
- 调整预制体权重比例

## 🎉 改进效果

### 修复前的问题
- ❌ 预制体大量重叠
- ❌ 大型建筑位置不合理
- ❌ 密度控制不准确

### 修复后的效果
- ✅ 零重叠，完美间隔
- ✅ 大型预制体正确占用空间
- ✅ 智能填充，高效利用空间
- ✅ 边界安全，不会超出范围

现在的生成算法完全解决了重叠问题，提供了真正智能的预制体布局！
