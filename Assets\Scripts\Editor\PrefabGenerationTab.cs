using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;

[System.Serializable]
public struct PrefabInfo
{
    public Vector2Int startCell;
    public Vector2Int gridSize;

    public PrefabInfo(Vector2Int start, Vector2Int size)
    {
        startCell = start;
        gridSize = size;
    }

    public RectInt GetBounds()
    {
        return new RectInt(startCell.x, startCell.y, gridSize.x, gridSize.y);
    }
}

[System.Serializable]
public class PrefabGenerationSettings : ScriptableObject
{
    [System.Serializable]
    public class GenerationSettings
    {
        public int maxCount = 50;
        public float minDistance = 2.0f;
        public bool useRandomSeed = true;
        public int seed = 0;
    }

    public GenerationSettings generationSettings = new GenerationSettings();
    public bool includeSubfolders = true;
}

public class PrefabGenerationTab
{
    [System.Serializable]
    public class FolderNode
    {
        public string folderName;
        public string folderPath;
        public List<GameObject> prefabs = new List<GameObject>();
        public List<FolderNode> subFolders = new List<FolderNode>();
        public FolderNode parent;
        public int depth;

        // 树形视图状态
        public bool isSelected = false;
        public bool isExpanded = false;
        public bool hasCheckedChildren = false; // 是否有选中的子项

        public FolderNode(string name, string path, FolderNode parentNode = null)
        {
            folderName = name;
            folderPath = path;
            parent = parentNode;
            depth = parent != null ? parent.depth + 1 : 0;
            LoadPrefabs();
        }

        public void LoadPrefabs()
        {
            prefabs.Clear();

            if (string.IsNullOrEmpty(folderPath) || !AssetDatabase.IsValidFolder(folderPath))
                return;

            // 只加载当前文件夹中的预制体（不包括子文件夹）
            string[] guids = AssetDatabase.FindAssets("t:Prefab", new[] { folderPath });

            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                // 确保预制体在当前文件夹中，而不是子文件夹中
                string prefabDir = Path.GetDirectoryName(path).Replace('\\', '/');
                if (prefabDir == folderPath)
                {
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    if (prefab != null)
                    {
                        prefabs.Add(prefab);
                    }
                }
            }
        }

        // 获取所有子文件夹中的预制体（递归）
        public List<GameObject> GetAllPrefabs()
        {
            List<GameObject> allPrefabs = new List<GameObject>(prefabs);

            foreach (var subFolder in subFolders)
            {
                allPrefabs.AddRange(subFolder.GetAllPrefabs());
            }

            return allPrefabs;
        }

        // 获取显示名称（包含预制体数量）
        public string GetDisplayName()
        {
            int totalPrefabs = GetAllPrefabs().Count;
            return $"{folderName} ({prefabs.Count}/{totalPrefabs})";
        }
    }

    // 简化的选择状态（保留用于兼容性）
    [System.Serializable]
    public class SelectionState
    {
        public List<string> selectedPaths = new List<string>();

        public void Clear()
        {
            selectedPaths.Clear();
        }
    }

    // 预制体设置
    [System.Serializable]
    public class PrefabSetting
    {
        public GameObject prefab;
        public float weight = 1f;
        public Vector3 rotationRange = Vector3.zero;
        public Vector3 scaleRange = Vector3.one;
        public bool randomRotation = true;
        public bool randomScale = false;
        public float yOffset = 0f;

        // 预制体占格信息
        public int gridWidth = 1;   // 宽度（格数）
        public int gridHeight = 1;  // 高度（格数）
        public Vector2Int gridSize => new Vector2Int(gridWidth, gridHeight);
    }



    // 私有字段
    private FolderNode rootNode;
    private List<FolderNode> selectedFolders = new List<FolderNode>();
    private List<PrefabSetting> prefabSettings = new List<PrefabSetting>();
    private PrefabGenerationSettings settings;

    // UI状态
    private Vector2 categoryScrollPosition;
    private Vector2 prefabScrollPosition;
    private Vector2 settingsScrollPosition;
    private Vector2 treeViewScrollPosition; // 树形视图滚动位置

    // 折叠状态
    private bool showCategorySettings = true;
    private bool showPrefabBrowser = true;
    private bool showGenerationSettings = true;

    // 预制体预览
    private Dictionary<GameObject, Texture2D> prefabPreviews = new Dictionary<GameObject, Texture2D>();
    private Dictionary<GameObject, bool> previewsLoading = new Dictionary<GameObject, bool>();

    // 根文件夹路径
    private string rootFolderPath = "Assets/Art/Environment/Prefabs";

    // 撤销功能相关
    private List<GameObject> lastGeneratedObjects = new List<GameObject>();
    private bool hasLastGeneration = false;



    // 初始化
    public void Initialize()
    {
        // 创建设置对象
        if (settings == null)
        {
            settings = ScriptableObject.CreateInstance<PrefabGenerationSettings>();
        }

        LoadFolderHierarchy();
        LoadGenerationSettings();
    }

    // 清理
    public void Cleanup()
    {
        SaveGenerationSettings();
        ClearPrefabPreviews();
    }

    // 绘制UI
    public void OnGUI()
    {
        // 分类设置
        DrawCategorySettings();

        EditorGUILayout.Space(10);

        // 预制体浏览器
        DrawPrefabBrowser();

        EditorGUILayout.Space(10);

        // 生成设置
        DrawGenerationSettings();
    }

    // 绘制分类设置
    private void DrawCategorySettings()
    {
        showCategorySettings = EditorGUILayout.Foldout(showCategorySettings, "分层文件夹选择", true, EditorStyles.foldoutHeader);

        if (showCategorySettings)
        {
            EditorGUI.indentLevel++;

            // 根文件夹设置
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("根文件夹", GUILayout.Width(80));
            string newRootPath = EditorGUILayout.TextField(rootFolderPath);
            if (GUILayout.Button("浏览", GUILayout.Width(60)))
            {
                string path = EditorUtility.OpenFolderPanel("选择预制体根文件夹", Application.dataPath, "");
                if (!string.IsNullOrEmpty(path))
                {
                    if (path.StartsWith(Application.dataPath))
                    {
                        path = "Assets" + path.Substring(Application.dataPath.Length);
                    }
                    newRootPath = path;
                }
            }
            EditorGUILayout.EndHorizontal();

            if (newRootPath != rootFolderPath)
            {
                rootFolderPath = newRootPath;
                LoadFolderHierarchy();
            }

            // 选项设置
            EditorGUILayout.BeginHorizontal();
            EditorGUI.BeginChangeCheck();
            bool newIncludeSubfolders = EditorGUILayout.Toggle("包含子文件夹", settings.includeSubfolders, GUILayout.Width(120));
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(settings, "Change Include Subfolders");
                settings.includeSubfolders = newIncludeSubfolders;
                UpdatePrefabSettings();
            }

            if (GUILayout.Button("刷新文件夹", GUILayout.Width(80)))
            {
                LoadFolderHierarchy();
            }
            EditorGUILayout.EndHorizontal();

            // 绘制分层下拉框
            if (rootNode != null)
            {
                DrawHierarchicalSelectors();
            }
            else
            {
                EditorGUILayout.HelpBox("请设置有效的根文件夹路径", MessageType.Warning);
            }

            EditorGUI.indentLevel--;
        }
    }

    // 绘制分层选择器（Unity Project窗口风格）
    private void DrawHierarchicalSelectors()
    {
        EditorGUILayout.LabelField("文件夹树形视图", EditorStyles.boldLabel);

        // 绘制Unity Project窗口风格的树形视图
        DrawProjectStyleTreeView();

        // 显示当前选择的信息
        DrawSelectionInfo();
    }

    // Unity Project窗口风格的树形视图
    private void DrawProjectStyleTreeView()
    {
        if (rootNode == null) return;

        // 树形视图容器
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);

        // 工具栏
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        if (GUILayout.Button("全部展开", EditorStyles.toolbarButton, GUILayout.Width(60)))
        {
            ExpandAllNodes(rootNode, true);
        }
        if (GUILayout.Button("全部折叠", EditorStyles.toolbarButton, GUILayout.Width(60)))
        {
            ExpandAllNodes(rootNode, false);
        }
        GUILayout.FlexibleSpace();
        if (GUILayout.Button("全选", EditorStyles.toolbarButton, GUILayout.Width(40)))
        {
            SelectAllNodes(rootNode, true);
            UpdateSelectedFoldersFromTree();
        }
        if (GUILayout.Button("全不选", EditorStyles.toolbarButton, GUILayout.Width(50)))
        {
            SelectAllNodes(rootNode, false);
            UpdateSelectedFoldersFromTree();
        }
        EditorGUILayout.EndHorizontal();

        // 树形视图滚动区域
        treeViewScrollPosition = EditorGUILayout.BeginScrollView(treeViewScrollPosition, GUILayout.Height(200));

        // 绘制根节点的子节点
        foreach (var childNode in rootNode.subFolders)
        {
            DrawTreeNode(childNode, 0);
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    // 绘制树形节点
    private void DrawTreeNode(FolderNode node, int indentLevel)
    {
        EditorGUILayout.BeginHorizontal();

        // 缩进
        GUILayout.Space(indentLevel * 16);

        // 折叠/展开箭头
        bool hasChildren = node.subFolders.Count > 0;
        if (hasChildren)
        {
            // 创建折叠箭头样式
            GUIStyle foldoutStyle = new GUIStyle(EditorStyles.foldout);
            foldoutStyle.margin = new RectOffset(0, 0, 0, 0);
            foldoutStyle.padding = new RectOffset(0, 0, 0, 0);

            bool newExpanded = GUILayout.Toggle(node.isExpanded, "", foldoutStyle, GUILayout.Width(12));
            if (newExpanded != node.isExpanded)
            {
                node.isExpanded = newExpanded;
            }
        }
        else
        {
            GUILayout.Space(12);
        }

        // 文件夹图标
        GUIContent folderIcon = EditorGUIUtility.IconContent("Folder Icon");
        GUILayout.Label(folderIcon, GUILayout.Width(16), GUILayout.Height(16));

        // 添加一些间距
        GUILayout.Space(4);

        // 复选框（使用按钮模拟，确保可点击）
        bool currentState = node.isSelected;
        bool newSelected = currentState;

        // 创建复选框按钮（简化为两种状态）
        string checkboxText;
        if (node.isSelected)
        {
            checkboxText = "☑"; // 选中状态
        }
        else
        {
            checkboxText = "☐"; // 未选中状态（包括混合状态）
        }

        // 使用按钮模拟复选框，确保可点击
        GUIStyle checkboxStyle = new GUIStyle(GUI.skin.label);
        checkboxStyle.fontSize = 14;
        checkboxStyle.alignment = TextAnchor.MiddleCenter;
        checkboxStyle.fixedWidth = 16;
        checkboxStyle.fixedHeight = 16;

        if (GUILayout.Button(checkboxText, checkboxStyle, GUILayout.Width(16), GUILayout.Height(16)))
        {
            newSelected = !currentState; // 切换状态
        }

        if (newSelected != currentState)
        {
            node.isSelected = newSelected;
            UpdateParentChildSelection(node);
            UpdateSelectedFoldersFromTree();
        }

        // 复选框和文件夹名之间的间距
        GUILayout.Space(12);

        // 文件夹名称和预制体数量
        string displayText = $"{node.folderName}";
        if (node.prefabs.Count > 0 || node.GetAllPrefabs().Count > 0)
        {
            int directCount = node.prefabs.Count;
            int totalCount = node.GetAllPrefabs().Count;
            displayText += $" ({directCount}/{totalCount})";
        }

        // 文件夹名称标签（可点击）
        GUIStyle labelStyle = new GUIStyle(EditorStyles.label);
        if (node.isSelected)
        {
            labelStyle.normal.textColor = Color.white;
        }

        if (GUILayout.Button(displayText, labelStyle))
        {
            // 点击文件夹名称时切换选择状态
            node.isSelected = !node.isSelected;
            UpdateParentChildSelection(node);
            UpdateSelectedFoldersFromTree();
        }

        EditorGUILayout.EndHorizontal();

        // 绘制子节点（如果展开）
        if (node.isExpanded && hasChildren)
        {
            foreach (var childNode in node.subFolders)
            {
                DrawTreeNode(childNode, indentLevel + 1);
            }
        }
    }

    // 更新父子选择状态
    private void UpdateParentChildSelection(FolderNode node)
    {
        // 如果选中当前节点，则选中所有子节点
        if (node.isSelected)
        {
            SelectAllChildNodes(node, true);
        }
        else
        {
            // 如果取消选中当前节点，则取消选中所有子节点
            SelectAllChildNodes(node, false);
        }

        // 只更新父节点的显示状态，不改变父节点的选择状态
        UpdateParentDisplayStatus(node.parent);
    }

    // 更新父节点显示状态（只更新显示状态，不改变选择状态）
    private void UpdateParentDisplayStatus(FolderNode parentNode)
    {
        if (parentNode == null) return;

        bool anyChildSelected = false;

        // 检查是否有任何子节点被选中或有选中的子节点
        foreach (var child in parentNode.subFolders)
        {
            if (child.isSelected || child.hasCheckedChildren)
            {
                anyChildSelected = true;
                break;
            }
        }

        // 只更新显示状态，不改变父节点的选择状态
        parentNode.hasCheckedChildren = anyChildSelected;

        // 递归更新上级父节点的显示状态
        UpdateParentDisplayStatus(parentNode.parent);
    }

    // 展开/折叠所有节点
    private void ExpandAllNodes(FolderNode node, bool expanded)
    {
        node.isExpanded = expanded;
        foreach (var child in node.subFolders)
        {
            ExpandAllNodes(child, expanded);
        }
    }

    // 选择/取消选择所有节点（包括当前节点）
    private void SelectAllNodes(FolderNode node, bool selected)
    {
        node.isSelected = selected;
        foreach (var child in node.subFolders)
        {
            SelectAllNodes(child, selected);
        }
    }

    // 只选择/取消选择子节点（不包括当前节点）
    private void SelectAllChildNodes(FolderNode node, bool selected)
    {
        foreach (var child in node.subFolders)
        {
            child.isSelected = selected;
            SelectAllChildNodes(child, selected);
        }
    }

    // 从树形视图更新选中的文件夹列表
    private void UpdateSelectedFoldersFromTree()
    {
        selectedFolders.Clear();
        CollectSelectedNodes(rootNode, selectedFolders);
        UpdatePrefabSettings();
        PreloadPrefabPreviews();
    }

    // 收集选中的节点
    private void CollectSelectedNodes(FolderNode node, List<FolderNode> selectedList)
    {
        if (node.isSelected)
        {
            selectedList.Add(node);
        }

        foreach (var child in node.subFolders)
        {
            CollectSelectedNodes(child, selectedList);
        }
    }



    // 显示选择信息
    private void DrawSelectionInfo()
    {
        EditorGUILayout.Space(10);

        if (selectedFolders.Count > 0)
        {
            // 显示预制体统计（更突出）
            int totalPrefabs = 0;
            foreach (var folder in selectedFolders)
            {
                totalPrefabs += settings.includeSubfolders ? folder.GetAllPrefabs().Count : folder.prefabs.Count;
            }

            // 统计信息框
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField($"已选择: {selectedFolders.Count} 个文件夹, {totalPrefabs} 个预制体", EditorStyles.boldLabel);
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(5);

            // 简化的选择列表（只显示文件夹名称）
            EditorGUILayout.LabelField("选中的文件夹:", EditorStyles.boldLabel);

            // 使用滚动视图，限制高度
            Vector2 selectionScrollPos = EditorGUILayout.BeginScrollView(Vector2.zero, GUILayout.Height(80));

            for (int i = selectedFolders.Count - 1; i >= 0; i--)
            {
                var folder = selectedFolders[i];
                EditorGUILayout.BeginHorizontal();

                // 只显示文件夹名称和预制体数量
                int folderPrefabCount = settings.includeSubfolders ? folder.GetAllPrefabs().Count : folder.prefabs.Count;
                EditorGUILayout.LabelField($"• {folder.folderName} ({folderPrefabCount})", EditorStyles.miniLabel);

                if (GUILayout.Button("×", GUILayout.Width(20), GUILayout.Height(16)))
                {
                    selectedFolders.RemoveAt(i);
                    UpdatePrefabSettings();
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space(5);

            // 操作按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("清除所有"))
            {
                selectedFolders.Clear();
                UpdatePrefabSettings();
            }

            if (GUILayout.Button("刷新预览"))
            {
                ClearPrefabPreviews();
                PreloadPrefabPreviews();
            }
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("请选择文件夹以开始", EditorStyles.centeredGreyMiniLabel);
            EditorGUILayout.EndVertical();
        }
    }

    // 绘制预制体浏览器
    private void DrawPrefabBrowser()
    {
        showPrefabBrowser = EditorGUILayout.Foldout(showPrefabBrowser, "预制体浏览器", true, EditorStyles.foldoutHeader);

        if (showPrefabBrowser)
        {
            EditorGUI.indentLevel++;



            if (selectedFolders.Count > 0)
            {
                prefabScrollPosition = EditorGUILayout.BeginScrollView(prefabScrollPosition, GUILayout.Height(200));

                foreach (var folder in selectedFolders)
                {
                    List<GameObject> prefabsToShow = settings.includeSubfolders ? folder.GetAllPrefabs() : folder.prefabs;

                    if (prefabsToShow.Count > 0)
                    {
                        string displayName = settings.includeSubfolders ?
                            $"{folder.folderName} (含子文件夹: {prefabsToShow.Count} 个预制体)" :
                            $"{folder.folderName} ({prefabsToShow.Count} 个预制体)";

                        EditorGUILayout.LabelField(displayName, EditorStyles.boldLabel);
                        DrawPrefabGrid(prefabsToShow);
                        EditorGUILayout.Space(5);
                    }
                }

                EditorGUILayout.EndScrollView();
            }
            else
            {
                EditorGUILayout.HelpBox("请先选择文件夹", MessageType.Info);
            }

            EditorGUI.indentLevel--;
        }
    }

    // 绘制预制体网格
    private void DrawPrefabGrid(List<GameObject> prefabs)
    {
        int columns = 4; // 固定4列
        int rows = Mathf.CeilToInt((float)prefabs.Count / columns);

        // 计算实际的预览框大小（包括边距和标签）
        int previewSize = 64; // 固定预览大小
        int itemWidth = previewSize + 10; // 预览图 + 边距
        int itemHeight = previewSize + 35; // 预览图 + 标签高度 + 格数信息高度

        for (int row = 0; row < rows; row++)
        {
            EditorGUILayout.BeginHorizontal();

            for (int col = 0; col < columns; col++)
            {
                int index = row * columns + col;
                if (index >= prefabs.Count)
                {
                    // 填充空白区域保持对齐
                    GUILayout.Space(itemWidth);
                    continue;
                }

                GameObject prefab = prefabs[index];
                DrawPrefabItem(prefab, itemWidth, itemHeight);
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(5);
        }
    }

    // 绘制单个预制体项目
    private void DrawPrefabItem(GameObject prefab, int itemWidth, int itemHeight)
    {
        EditorGUILayout.BeginVertical(GUILayout.Width(itemWidth), GUILayout.Height(itemHeight));

        // 获取预览图
        Texture2D preview = GetPrefabPreview(prefab);

        // 创建样式
        GUIStyle previewStyle = new GUIStyle(GUI.skin.button);
        previewStyle.padding = new RectOffset(2, 2, 2, 2);
        previewStyle.margin = new RectOffset(2, 2, 2, 2);

        // 检查是否被选中
        bool isSelected = Selection.activeObject == prefab;
        if (isSelected)
        {
            previewStyle.normal.background = MakeColorTexture(new Color(0.3f, 0.5f, 1f, 0.3f));
        }

        // 绘制预览按钮
        int previewSize = 64; // 固定预览大小
        GUIContent content = new GUIContent(preview);
        if (GUILayout.Button(content, previewStyle, GUILayout.Width(previewSize), GUILayout.Height(previewSize)))
        {
            // 点击预制体时的操作
            Selection.activeObject = prefab;
            EditorGUIUtility.PingObject(prefab);
        }

        // 绘制预制体名称
        GUIStyle labelStyle = new GUIStyle(EditorStyles.miniLabel);
        labelStyle.alignment = TextAnchor.MiddleCenter;
        labelStyle.wordWrap = true;
        labelStyle.fontSize = 9;

        // 截断过长的名称
        string displayName = prefab.name;
        if (displayName.Length > 12)
        {
            displayName = displayName.Substring(0, 9) + "...";
        }

        // 获取并显示格数信息
        Vector2Int gridSize = GetPrefabGridSize(prefab);
        string gridInfo = $"{gridSize.x}x{gridSize.y}";

        EditorGUILayout.LabelField(displayName, labelStyle, GUILayout.Width(previewSize), GUILayout.Height(12));

        // 显示格数信息
        GUIStyle gridStyle = new GUIStyle(EditorStyles.miniLabel);
        gridStyle.alignment = TextAnchor.MiddleCenter;
        gridStyle.fontSize = 8;
        gridStyle.normal.textColor = Color.gray;

        EditorGUILayout.LabelField(gridInfo, gridStyle, GUILayout.Width(previewSize), GUILayout.Height(10));

        EditorGUILayout.EndVertical();
    }

    // 创建纯色纹理
    private Texture2D MakeColorTexture(Color color)
    {
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, color);
        texture.Apply();
        return texture;
    }

    // 获取预制体预览图
    private Texture2D GetPrefabPreview(GameObject prefab)
    {
        if (!prefabPreviews.ContainsKey(prefab))
        {
            Texture2D preview = null;

            // 简化预览生成：优先使用AssetPreview
            preview = AssetPreview.GetAssetPreview(prefab);

            // 如果AssetPreview不可用，使用自定义渲染
            if (preview == null)
            {
                preview = GenerateCustomPreview(prefab);
            }

            prefabPreviews[prefab] = preview;
        }

        // 如果预览图为null，显示加载中或默认图标
        Texture2D result = prefabPreviews[prefab];
        if (result == null)
        {
            // 检查是否正在加载
            if (previewsLoading.ContainsKey(prefab) && previewsLoading[prefab])
            {
                return EditorGUIUtility.FindTexture("WaitSpin00");
            }
            else
            {
                return EditorGUIUtility.FindTexture("Prefab Icon");
            }
        }

        return result;
    }

    // 生成自定义预览图
    private Texture2D GenerateCustomPreview(GameObject prefab)
    {
        try
        {
            // 创建临时场景对象来渲染预览
            GameObject tempInstance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            if (tempInstance == null) return null;

            // 获取对象的边界
            Bounds bounds = GetObjectBounds(tempInstance);
            if (bounds.size == Vector3.zero)
            {
                Object.DestroyImmediate(tempInstance);
                return null;
            }

            // 设置临时摄像机
            GameObject cameraGO = new GameObject("PreviewCamera");
            Camera camera = cameraGO.AddComponent<Camera>();

            // 配置摄像机
            camera.clearFlags = CameraClearFlags.SolidColor;
            camera.backgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f); // 深灰色背景，类似Asset Store
            camera.orthographic = true;
            camera.cullingMask = -1;
            camera.nearClipPlane = 0.1f;
            camera.farClipPlane = 1000f;

            // 计算摄像机位置和大小
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;
            float maxSize = Mathf.Max(size.x, size.y, size.z);

            // 设置摄像机位置（从斜上方观察，类似Asset Store的角度）
            Vector3 offset = new Vector3(maxSize * 0.6f, maxSize * 0.8f, maxSize * 0.6f);
            camera.transform.position = center + offset;
            camera.transform.LookAt(center);

            // 设置正交大小，确保对象完全可见
            camera.orthographicSize = maxSize * 0.7f;

            // 添加简单的光照
            GameObject lightGO = new GameObject("PreviewLight");
            Light light = lightGO.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1.2f;
            light.color = Color.white;
            light.transform.rotation = Quaternion.Euler(45f, 45f, 0f);

            // 创建渲染纹理
            int resolution = 256; // 高分辨率预览
            RenderTexture renderTexture = new RenderTexture(resolution, resolution, 24);
            renderTexture.antiAliasing = 4; // 抗锯齿
            camera.targetTexture = renderTexture;

            // 渲染
            camera.Render();

            // 读取渲染结果
            RenderTexture.active = renderTexture;
            Texture2D preview = new Texture2D(resolution, resolution, TextureFormat.RGBA32, false);
            preview.name = $"CustomPreview_{prefab.name}"; // 添加标识
            preview.ReadPixels(new Rect(0, 0, resolution, resolution), 0, 0);
            preview.Apply();

            // 清理
            RenderTexture.active = null;
            camera.targetTexture = null;
            Object.DestroyImmediate(renderTexture);
            Object.DestroyImmediate(lightGO);
            Object.DestroyImmediate(cameraGO);
            Object.DestroyImmediate(tempInstance);

            return preview;
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"生成预制体预览失败: {prefab.name}, 错误: {e.Message}");
            return null;
        }
    }

    // 获取对象边界
    private Bounds GetObjectBounds(GameObject obj)
    {
        Bounds bounds = new Bounds();
        bool hasBounds = false;

        // 获取所有渲染器
        Renderer[] renderers = obj.GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            if (renderer.bounds.size != Vector3.zero)
            {
                if (!hasBounds)
                {
                    bounds = renderer.bounds;
                    hasBounds = true;
                }
                else
                {
                    bounds.Encapsulate(renderer.bounds);
                }
            }
        }

        // 如果没有渲染器，尝试使用碰撞器
        if (!hasBounds)
        {
            Collider[] colliders = obj.GetComponentsInChildren<Collider>();
            foreach (Collider collider in colliders)
            {
                if (collider.bounds.size != Vector3.zero)
                {
                    if (!hasBounds)
                    {
                        bounds = collider.bounds;
                        hasBounds = true;
                    }
                    else
                    {
                        bounds.Encapsulate(collider.bounds);
                    }
                }
            }
        }

        return bounds;
    }

    // 清理预制体预览
    private void ClearPrefabPreviews()
    {
        // 清理自定义生成的纹理
        foreach (var kvp in prefabPreviews)
        {
            if (kvp.Value != null && kvp.Value.name.Contains("Custom"))
            {
                Object.DestroyImmediate(kvp.Value);
            }
        }

        prefabPreviews.Clear();
        previewsLoading.Clear();
    }

    // 预加载预制体预览（异步）
    private void PreloadPrefabPreviews()
    {
        foreach (var folder in selectedFolders)
        {
            List<GameObject> prefabsToLoad = settings.includeSubfolders ? folder.GetAllPrefabs() : folder.prefabs;

            foreach (var prefab in prefabsToLoad)
            {
                if (!prefabPreviews.ContainsKey(prefab) && !previewsLoading.ContainsKey(prefab))
                {
                    previewsLoading[prefab] = true;
                    // 在下一帧开始加载预览
                    EditorApplication.delayCall += () => LoadPreviewAsync(prefab);
                }
            }
        }
    }

    // 异步加载预览
    private void LoadPreviewAsync(GameObject prefab)
    {
        if (prefab == null) return;

        try
        {
            Texture2D preview = null;

            // 简化预览生成：优先使用AssetPreview
            preview = AssetPreview.GetAssetPreview(prefab);
            if (preview == null)
            {
                preview = GenerateCustomPreview(prefab);
            }

            prefabPreviews[prefab] = preview;
            previewsLoading[prefab] = false;

            // 重绘界面
            EditorApplication.delayCall += () => {
                if (EditorWindow.HasOpenInstances<PrefabGeneratorToolbox>())
                {
                    EditorWindow.GetWindow<PrefabGeneratorToolbox>().Repaint();
                }
            };
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"异步加载预制体预览失败: {prefab.name}, 错误: {e.Message}");
            previewsLoading[prefab] = false;
        }
    }

    // 绘制生成设置
    private void DrawGenerationSettings()
    {
        showGenerationSettings = EditorGUILayout.Foldout(showGenerationSettings, "生成设置", true, EditorStyles.foldoutHeader);

        if (showGenerationSettings)
        {
            EditorGUI.indentLevel++;

            // 清除生成的预制体按钮
            EditorGUILayout.BeginHorizontal();

            GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
            buttonStyle.fixedHeight = 25;

            GUILayout.FlexibleSpace();

            GUI.enabled = hasLastGeneration;
            if (GUILayout.Button("清除生成", buttonStyle, GUILayout.Width(80)))
            {
                ClearLastGeneration();
            }
            GUI.enabled = true;

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(5);

            // 最大生成数量设置
            EditorGUILayout.LabelField("最大生成数量");
            EditorGUI.BeginChangeCheck();
            int newMaxCount = EditorGUILayout.IntSlider(settings.generationSettings.maxCount, 1, 200);
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(settings, "Change Max Count");
                settings.generationSettings.maxCount = newMaxCount;
            }

            // 添加数量说明
            EditorGUILayout.HelpBox($"将尝试生成最多 {settings.generationSettings.maxCount} 个预制体", MessageType.Info);

            // 最小间距
            EditorGUILayout.LabelField("最小间距 (边缘距离)");
            EditorGUI.BeginChangeCheck();
            float newMinDistance = EditorGUILayout.Slider(settings.generationSettings.minDistance, 0f, 10f);
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(settings, "Change Min Distance");
                settings.generationSettings.minDistance = newMinDistance;
            }
            EditorGUILayout.HelpBox("控制预制体边缘之间的最小距离（格子单位），0表示可以紧邻", MessageType.Info);

            // 随机种子
            EditorGUI.BeginChangeCheck();
            bool newUseRandomSeed = EditorGUILayout.Toggle("使用随机种子", settings.generationSettings.useRandomSeed);
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(settings, "Change Use Random Seed");
                settings.generationSettings.useRandomSeed = newUseRandomSeed;
            }

            if (!settings.generationSettings.useRandomSeed)
            {
                EditorGUI.indentLevel++;
                EditorGUI.BeginChangeCheck();
                int newSeed = EditorGUILayout.IntField("随机种子", settings.generationSettings.seed);
                if (EditorGUI.EndChangeCheck())
                {
                    Undo.RecordObject(settings, "Change Seed");
                    settings.generationSettings.seed = newSeed;
                }
                EditorGUI.indentLevel--;
            }

            EditorGUI.indentLevel--;
        }
    }



    // 加载文件夹层次结构
    private void LoadFolderHierarchy()
    {
        selectedFolders.Clear();

        if (string.IsNullOrEmpty(rootFolderPath) || !AssetDatabase.IsValidFolder(rootFolderPath))
        {
            Debug.LogWarning($"无效的根文件夹路径: {rootFolderPath}");
            rootNode = null;
            return;
        }

        // 创建根节点
        string rootName = Path.GetFileName(rootFolderPath);
        rootNode = new FolderNode(rootName, rootFolderPath);

        // 递归构建文件夹树
        BuildFolderTree(rootNode);

        Debug.Log($"已加载文件夹层次结构，根节点: {rootName}，共 {CountAllFolders(rootNode)} 个文件夹");
    }

    // 计算文件夹总数
    private int CountAllFolders(FolderNode node)
    {
        int count = 1; // 当前节点
        foreach (var subFolder in node.subFolders)
        {
            count += CountAllFolders(subFolder);
        }
        return count;
    }

    // 递归构建文件夹树
    private void BuildFolderTree(FolderNode parentNode)
    {
        string[] subFolders = AssetDatabase.GetSubFolders(parentNode.folderPath);

        foreach (string folderPath in subFolders)
        {
            string folderName = Path.GetFileName(folderPath);
            FolderNode childNode = new FolderNode(folderName, folderPath, parentNode);

            parentNode.subFolders.Add(childNode);

            // 递归构建子文件夹
            BuildFolderTree(childNode);
        }
    }



    // 更新预制体设置
    private void UpdatePrefabSettings()
    {
        prefabSettings.Clear();

        foreach (var folder in selectedFolders)
        {
            List<GameObject> prefabsToAdd = settings.includeSubfolders ? folder.GetAllPrefabs() : folder.prefabs;

            foreach (var prefab in prefabsToAdd)
            {
                PrefabSetting setting = new PrefabSetting();
                setting.prefab = prefab;

                // 自动解析预制体的格数信息
                Vector2Int gridSize = GetPrefabGridSize(prefab);
                setting.gridWidth = gridSize.x;
                setting.gridHeight = gridSize.y;

                prefabSettings.Add(setting);
            }
        }

        Debug.Log($"已更新预制体设置，共 {prefabSettings.Count} 个预制体");

        // 输出格数信息用于调试
        foreach (var setting in prefabSettings)
        {
            Debug.Log($"预制体: {setting.prefab.name}, 格数: {setting.gridWidth}x{setting.gridHeight}");
        }
    }

    // 加载生成设置
    private void LoadGenerationSettings()
    {
        // 这里可以实现从EditorPrefs或文件加载设置
        settings.generationSettings = new PrefabGenerationSettings.GenerationSettings();
    }

    // 保存生成设置
    private void SaveGenerationSettings()
    {
        // 这里可以实现保存设置到EditorPrefs或文件
    }

    // 生成预制体（考虑格数信息避免重叠）
    public void GeneratePrefabs(HashSet<Vector2Int> selectedCells)
    {
        // 检查选中区域
        if (selectedCells == null || selectedCells.Count == 0)
        {
            EditorUtility.DisplayDialog("错误", "没有选中的区域", "确定");
            return;
        }

        // 检查预制体设置
        if (prefabSettings == null || prefabSettings.Count == 0)
        {
            EditorUtility.DisplayDialog("错误", "没有选择预制体分类或分类中没有预制体", "确定");
            return;
        }

        // 清除上次生成记录，准备记录新的生成
        lastGeneratedObjects.Clear();
        hasLastGeneration = false;

        // 设置Unity Undo系统
        Undo.IncrementCurrentGroup();
        Undo.SetCurrentGroupName("Generate Prefabs");
        int undoGroupIndex = Undo.GetCurrentGroup();

        // 执行智能生成逻辑
        int generatedCount = 0;
        int attemptedCount = 0;
        int skippedByDistance = 0;
        int skippedByOccupied = 0;
        int maxCount = settings.generationSettings.maxCount;

        System.Random random = settings.generationSettings.useRandomSeed ? new System.Random() : new System.Random(settings.generationSettings.seed);

        // 创建占用网格来跟踪已占用的位置
        HashSet<Vector2Int> occupiedCells = new HashSet<Vector2Int>();

        // 记录已生成预制体的信息（用于最小间距检查）
        List<PrefabInfo> generatedPrefabs = new List<PrefabInfo>();

        // 将选中的格子转换为列表并随机排序
        List<Vector2Int> cellList = new List<Vector2Int>(selectedCells);
        for (int i = 0; i < cellList.Count; i++)
        {
            int randomIndex = random.Next(i, cellList.Count);
            Vector2Int temp = cellList[i];
            cellList[i] = cellList[randomIndex];
            cellList[randomIndex] = temp;
        }

        foreach (var cell in cellList)
        {
            // 检查是否已达到最大生成数量
            if (generatedCount >= maxCount)
                break;

            attemptedCount++;

            // 检查此位置是否已被占用
            if (occupiedCells.Contains(cell))
            {
                skippedByOccupied++;
                continue;
            }

            // 随机选择一个预制体
            PrefabSetting selectedSetting = prefabSettings[random.Next(prefabSettings.Count)];

            // 检查预制体是否能放置在此位置（考虑格数和最小间距）
            if (!CanPlacePrefabAt(cell, selectedSetting, occupiedCells, selectedCells, generatedPrefabs))
            {
                skippedByDistance++;
                continue;
            }

            // 标记预制体占用的所有格子
            MarkOccupiedCells(cell, selectedSetting, occupiedCells);

            // 计算生成位置（预制体中心）
            Vector3 position = CalculatePrefabPosition(cell, selectedSetting);

            // 记录预制体信息（用于最小间距检查）
            PrefabInfo prefabInfo = new PrefabInfo(cell, selectedSetting.gridSize);
            generatedPrefabs.Add(prefabInfo);

            // 随机旋转
            Quaternion rotation = Quaternion.identity;
            if (selectedSetting.randomRotation)
            {
                rotation = Quaternion.Euler(
                    Random.Range(-selectedSetting.rotationRange.x, selectedSetting.rotationRange.x),
                    Random.Range(-selectedSetting.rotationRange.y, selectedSetting.rotationRange.y),
                    Random.Range(-selectedSetting.rotationRange.z, selectedSetting.rotationRange.z)
                );
            }

            // 实例化预制体
            GameObject instance = PrefabUtility.InstantiatePrefab(selectedSetting.prefab) as GameObject;
            instance.transform.position = position;
            instance.transform.rotation = rotation;

            // 注册到Unity Undo系统
            Undo.RegisterCreatedObjectUndo(instance, "Generate Prefab");

            // 记录生成的对象用于撤销
            lastGeneratedObjects.Add(instance);

            // 随机缩放
            if (selectedSetting.randomScale)
            {
                Vector3 scale = Vector3.one;
                scale.x *= Random.Range(selectedSetting.scaleRange.x, selectedSetting.scaleRange.y);
                scale.y *= Random.Range(selectedSetting.scaleRange.x, selectedSetting.scaleRange.y);
                scale.z *= Random.Range(selectedSetting.scaleRange.x, selectedSetting.scaleRange.y);
                instance.transform.localScale = scale;
            }

            generatedCount++;
        }

        // 标记有上次生成的记录
        hasLastGeneration = generatedCount > 0;

        // 合并Undo操作
        Undo.CollapseUndoOperations(undoGroupIndex);

        // 显示详细的生成统计
        string statisticsMessage = $"生成完成！\n\n" +
            $"✅ 成功生成: {generatedCount} 个预制体\n" +
            $"🎯 目标数量: {maxCount} 个\n" +
            $"📊 尝试位置: {attemptedCount} 个\n" +
            $"📏 间距限制: {skippedByDistance} 个 ({(float)skippedByDistance/attemptedCount*100:F1}%)\n" +
            $"🚫 位置占用: {skippedByOccupied} 个 ({(float)skippedByOccupied/attemptedCount*100:F1}%)\n" +
            $"📈 完成率: {(float)generatedCount/maxCount*100:F1}%";

        EditorUtility.DisplayDialog("生成统计", statisticsMessage, "确定");
    }

    // 撤销上次生成
    public void UndoLastGeneration()
    {
        if (!hasLastGeneration || lastGeneratedObjects.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", "没有可撤销的生成操作", "确定");
            return;
        }

        int deletedCount = 0;

        // 删除所有上次生成的对象
        for (int i = lastGeneratedObjects.Count - 1; i >= 0; i--)
        {
            GameObject obj = lastGeneratedObjects[i];
            if (obj != null)
            {
                Object.DestroyImmediate(obj);
                deletedCount++;
            }
        }

        // 清除记录
        lastGeneratedObjects.Clear();
        hasLastGeneration = false;

        EditorUtility.DisplayDialog("撤销完成", $"已删除 {deletedCount} 个预制体实例", "确定");
    }

    // 清除所有生成的预制体（可选功能）
    public void ClearAllGeneratedPrefabs()
    {
        if (EditorUtility.DisplayDialog("确认清除", "确定要清除场景中所有生成的预制体吗？\n此操作无法撤销！", "确定", "取消"))
        {
            // 查找所有可能是生成的预制体
            GameObject[] allObjects = Object.FindObjectsOfType<GameObject>();
            int deletedCount = 0;

            foreach (GameObject obj in allObjects)
            {
                // 检查是否是预制体实例
                if (PrefabUtility.IsPartOfAnyPrefab(obj) && obj.transform.parent == null)
                {
                    // 可以添加更多条件来识别是否是工具生成的预制体
                    Object.DestroyImmediate(obj);
                    deletedCount++;
                }
            }

            // 清除记录
            lastGeneratedObjects.Clear();
            hasLastGeneration = false;

            EditorUtility.DisplayDialog("清除完成", $"已删除 {deletedCount} 个预制体实例", "确定");
        }
    }

    // 检查是否有上次生成的记录
    public bool HasLastGeneration()
    {
        return hasLastGeneration && lastGeneratedObjects.Count > 0;
    }

    // 解析文件夹名称中的格数信息
    private Vector2Int ParseGridSizeFromFolderName(string folderName)
    {
        // 查找格式如 "3x3", "2x4", "1x1" 等的模式
        var match = System.Text.RegularExpressions.Regex.Match(folderName, @"(\d+)x(\d+)");

        if (match.Success)
        {
            int width = int.Parse(match.Groups[1].Value);
            int height = int.Parse(match.Groups[2].Value);
            return new Vector2Int(width, height);
        }

        // 如果没有找到格数信息，默认为1x1
        return new Vector2Int(1, 1);
    }

    // 获取预制体的格数信息（从其父文件夹名称解析）
    private Vector2Int GetPrefabGridSize(GameObject prefab)
    {
        string prefabPath = AssetDatabase.GetAssetPath(prefab);
        string folderPath = System.IO.Path.GetDirectoryName(prefabPath);
        string folderName = System.IO.Path.GetFileName(folderPath);

        return ParseGridSizeFromFolderName(folderName);
    }

    // 检查预制体是否能放置在指定位置（包括最小间距检查）
    private bool CanPlacePrefabAt(Vector2Int startCell, PrefabSetting prefabSetting, HashSet<Vector2Int> occupiedCells, HashSet<Vector2Int> selectedCells, List<PrefabInfo> generatedPrefabs)
    {
        Vector2Int gridSize = prefabSetting.gridSize;
        float minDistance = settings.generationSettings.minDistance;

        // 检查预制体占用的所有格子
        for (int x = 0; x < gridSize.x; x++)
        {
            for (int y = 0; y < gridSize.y; y++)
            {
                Vector2Int checkCell = startCell + new Vector2Int(x, y);

                // 检查格子是否在选中区域内
                if (!selectedCells.Contains(checkCell))
                    return false;

                // 检查格子是否已被占用
                if (occupiedCells.Contains(checkCell))
                    return false;
            }
        }

        // 检查最小间距（边缘到边缘的距离）
        if (minDistance > 0 && generatedPrefabs.Count > 0)
        {
            // 当前预制体的边界框
            RectInt currentBounds = new RectInt(startCell.x, startCell.y, gridSize.x, gridSize.y);

            // 检查与所有已生成预制体的边缘距离
            foreach (PrefabInfo existingPrefab in generatedPrefabs)
            {
                RectInt existingBounds = existingPrefab.GetBounds();

                // 计算两个矩形边缘之间的最小距离
                float distance = CalculateRectDistance(currentBounds, existingBounds);

                if (distance < minDistance)
                    return false;
            }
        }

        return true;
    }

    // 计算两个矩形边缘之间的最小距离
    private float CalculateRectDistance(RectInt rect1, RectInt rect2)
    {
        // 如果矩形重叠，距离为0
        if (rect1.Overlaps(rect2))
            return 0f;

        // 计算水平和垂直方向的间隙
        float horizontalGap = 0f;
        float verticalGap = 0f;

        // 水平间隙
        if (rect1.xMax <= rect2.xMin)
        {
            // rect1在rect2左边
            horizontalGap = rect2.xMin - rect1.xMax;
        }
        else if (rect2.xMax <= rect1.xMin)
        {
            // rect2在rect1左边
            horizontalGap = rect1.xMin - rect2.xMax;
        }

        // 垂直间隙
        if (rect1.yMax <= rect2.yMin)
        {
            // rect1在rect2下边
            verticalGap = rect2.yMin - rect1.yMax;
        }
        else if (rect2.yMax <= rect1.yMin)
        {
            // rect2在rect1下边
            verticalGap = rect1.yMin - rect2.yMax;
        }

        // 如果只有一个方向有间隙，返回该间隙
        if (horizontalGap > 0 && verticalGap == 0)
            return horizontalGap;
        if (verticalGap > 0 && horizontalGap == 0)
            return verticalGap;

        // 如果两个方向都有间隙，返回对角线距离
        if (horizontalGap > 0 && verticalGap > 0)
            return Mathf.Sqrt(horizontalGap * horizontalGap + verticalGap * verticalGap);

        // 其他情况（不应该发生）
        return 0f;
    }

    // 标记预制体占用的所有格子
    private void MarkOccupiedCells(Vector2Int startCell, PrefabSetting prefabSetting, HashSet<Vector2Int> occupiedCells)
    {
        Vector2Int gridSize = prefabSetting.gridSize;

        for (int x = 0; x < gridSize.x; x++)
        {
            for (int y = 0; y < gridSize.y; y++)
            {
                Vector2Int occupiedCell = startCell + new Vector2Int(x, y);
                occupiedCells.Add(occupiedCell);
            }
        }
    }

    // 计算预制体的世界位置（考虑格数，放置在中心）
    private Vector3 CalculatePrefabPosition(Vector2Int startCell, PrefabSetting prefabSetting)
    {
        Vector2Int gridSize = prefabSetting.gridSize;

        // 计算预制体的中心位置
        float centerX = startCell.x + (gridSize.x - 1) * 0.5f;
        float centerZ = startCell.y + (gridSize.y - 1) * 0.5f;

        return new Vector3(centerX, 0, centerZ);
    }

    // 场景GUI事件
    public void OnSceneGUI(SceneView sceneView)
    {
        // 预览生成区域或其他场景交互
    }



    // 清除最后生成的预制体
    public void ClearLastGeneration()
    {
        if (hasLastGeneration && lastGeneratedObjects.Count > 0)
        {
            // 使用Unity的Undo系统来删除对象
            Undo.IncrementCurrentGroup();
            Undo.SetCurrentGroupName("Clear Generated Prefabs");

            foreach (GameObject obj in lastGeneratedObjects)
            {
                if (obj != null)
                {
                    Undo.DestroyObjectImmediate(obj);
                }
            }

            Undo.CollapseUndoOperations(Undo.GetCurrentGroup());

            lastGeneratedObjects.Clear();
            hasLastGeneration = false;

            Debug.Log("已清除生成的预制体");
        }
    }


}
