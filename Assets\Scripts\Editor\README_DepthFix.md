# 深度关系修复功能

## 🔧 问题解决

**原问题**: 生成的红色绿色网格都遮挡住了模型，没有正确的深度关系

**解决方案**: 实现地面检测和深度测试，确保网格和区域预览正确贴在地面上

## 🎯 核心改进

### 1. 地面高度检测
**功能**: 自动检测地面高度，确保网格贴在实际地形上
**实现**: 使用射线检测从上方向下检测地面

```csharp
private float GetGroundHeight(float x, float z)
{
    Vector3 rayStart = new Vector3(x, 1000f, z);
    if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2000f))
    {
        return hit.point.y;
    }
    return 0f; // 默认地面高度
}
```

### 2. 深度测试设置
**功能**: 确保网格元素正确的深度关系
**实现**: 使用 `Handles.zTest` 控制深度测试

```csharp
// 设置深度测试，确保绘制在地面上
Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

// 绘制网格元素...

// 恢复默认深度测试
Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
```

## 🎨 修复效果对比

### 修复前的问题
```
问题现象：
- 网格线悬浮在空中
- 区域预览遮挡模型
- 深度关系错误
- 视觉效果不自然

┌─────────────┐  ← 网格悬浮在空中
│ 🟢🟢🟢🟢🟢 │     遮挡了下方的模型
│ 🟢🟢🟢🟢🟢 │  🏠 ← 模型被遮挡
│ 🟢🟢🟢🟢🟢 │
└─────────────┘
```

### 修复后的效果
```
正确效果：
- 网格线贴在地面上
- 区域预览跟随地形
- 正确的深度关系
- 自然的视觉效果

     🏠 ← 模型清晰可见
┌─────────────┐  ← 网格贴在地面上
│ ═══════════ │     不遮挡模型
│ ═══════════ │
└─────────────┘
```

## 🔧 技术实现细节

### 1. 网格线绘制改进
**原实现**:
```csharp
// 固定Y坐标为0
Vector3 start = new Vector3(startX * GRID_SIZE, 0, z * GRID_SIZE);
Vector3 end = new Vector3(endX * GRID_SIZE, 0, z * GRID_SIZE);
```

**新实现**:
```csharp
// 动态检测地面高度
float startY = GetGroundHeight(startX * GRID_SIZE, z * GRID_SIZE);
float endY = GetGroundHeight(endX * GRID_SIZE, z * GRID_SIZE);
Vector3 start = new Vector3(startX * GRID_SIZE, startY, z * GRID_SIZE);
Vector3 end = new Vector3(endX * GRID_SIZE, endY, z * GRID_SIZE);
```

### 2. 区域预览改进
**原实现**:
```csharp
// 固定Y坐标
Vector3[] corners = new Vector3[]
{
    new Vector3(cell.x * GRID_SIZE, 0, cell.y * GRID_SIZE),
    // ...
};
```

**新实现**:
```csharp
// 动态地面高度
float groundY = GetGroundHeight(cell.x * GRID_SIZE + GRID_SIZE * 0.5f, cell.y * GRID_SIZE + GRID_SIZE * 0.5f);
Vector3[] corners = new Vector3[]
{
    new Vector3(cell.x * GRID_SIZE, groundY, cell.y * GRID_SIZE),
    // ...
};
```

### 3. 深度测试控制
**设置深度测试**:
```csharp
// 确保网格绘制在地面上，不遮挡其他对象
Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
```

**深度测试模式说明**:
- `LessEqual`: 只有当深度小于或等于现有深度时才绘制
- `Always`: 总是绘制，忽略深度（默认模式）

## 📋 适用场景

### 1. 平坦地形
```
地面高度一致：
- 网格线完美对齐
- 区域预览平整
- 视觉效果统一
```

### 2. 起伏地形
```
地面高度变化：
- 网格线跟随地形起伏
- 区域预览贴合地面
- 自然的视觉效果
```

### 3. 复杂地形
```
多层地形结构：
- 射线检测最上层地面
- 网格贴在可见表面
- 避免穿透地形
```

## 🎯 功能优势

### 1. 视觉真实性
- ✅ **地形适应**: 网格自动适应地形高度变化
- ✅ **深度正确**: 正确的前后遮挡关系
- ✅ **自然外观**: 网格看起来像真正贴在地面上

### 2. 操作准确性
- ✅ **精确定位**: 网格位置与实际地面一致
- ✅ **可见性好**: 模型不被网格遮挡
- ✅ **参考准确**: 网格提供准确的空间参考

### 3. 性能优化
- ✅ **智能检测**: 只在需要时进行射线检测
- ✅ **缓存友好**: 相邻网格点可以复用检测结果
- ✅ **渲染优化**: 正确的深度测试减少不必要的绘制

## 🔍 技术细节

### 射线检测参数
```csharp
Vector3 rayStart = new Vector3(x, 1000f, z);  // 从高处开始
Vector3 rayDirection = Vector3.down;          // 向下检测
float maxDistance = 2000f;                    // 最大检测距离
```

### 深度偏移
```csharp
// 预览模式添加小偏移，避免Z-fighting
groundY + 0.01f
```

### 错误处理
```csharp
if (Physics.Raycast(...))
{
    return hit.point.y;  // 检测到地面
}
return 0f;  // 默认地面高度
```

## 💡 使用建议

### 最佳实践
1. **确保碰撞体**: 地形需要有Collider组件才能被检测
2. **合理层级**: 使用Layer来控制射线检测的目标
3. **性能考虑**: 在大范围网格时考虑降低检测频率

### 注意事项
- ✅ 地形必须有Collider组件
- ✅ 射线检测会有一定的性能开销
- ✅ 复杂地形可能需要调整检测参数
- ⚠️ 没有Collider的地形会使用默认高度0

## 🎉 改进效果

### 修复前的问题
- ❌ 网格悬浮在空中，不真实
- ❌ 区域预览遮挡模型，影响查看
- ❌ 深度关系错误，视觉混乱

### 修复后的效果
- ✅ 网格贴在地面上，真实自然
- ✅ 正确的深度关系，不遮挡模型
- ✅ 区域预览跟随地形，视觉准确
- ✅ 整体视觉效果专业和谐

现在的网格和区域预览系统具有正确的深度关系，提供了真实、准确、不干扰的视觉体验！
