using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// 场景预制体生成工具箱，集成所有预制体生成相关功能
/// </summary>
public class PrefabGeneratorToolbox : EditorWindow
{
    // 标签页枚举
    public enum ToolboxTab
    {
        AreaSelector,        // 区域选择
        PrefabGeneration     // 预制体生成（整合原来的预制体设置、生成设置、生成预览）
    }

    // 当前选中的标签页
    private ToolboxTab currentTab = ToolboxTab.AreaSelector;

    // 标签页图标
    private Texture2D[] tabIcons;

    // 标签页内容
    private AreaSelectorTab areaSelectorTab;
    private PrefabGenerationTab prefabGenerationTab;

    // 滚动位置
    private Vector2 scrollPosition;

    // 打开窗口的菜单项 - 放在 Tools 菜单下
    [MenuItem("Tools/预制体生成/场景预制体生成工具箱 %#a")] // Ctrl+Alt+A 快捷键（与原区域选择工具一致）
    public static void ShowWindow()
    {
        PrefabGeneratorToolbox window = GetWindow<PrefabGeneratorToolbox>("场景预制体生成工具箱");
        window.minSize = new Vector2(400, 600); // 设置最小窗口大小

        // 设置窗口图标
        Texture2D icon = EditorGUIUtility.FindTexture("Prefab Icon");
        if (icon != null)
        {
            window.titleContent = new GUIContent("预制体生成工具箱", icon);
        }

        window.Show();
    }

    // 添加到Unity顶部菜单
    [MenuItem("Window/预制体生成工具/场景预制体生成工具箱")]
    public static void ShowWindowFromMenu()
    {
        ShowWindow();
    }

    // 添加到右键上下文菜单（与原区域选择工具一致）
    [MenuItem("GameObject/预制体工具/打开预制体生成工具箱", false, 10)]
    public static void ShowWindowFromContext()
    {
        ShowWindow();
    }

    private void OnEnable()
    {
        // 初始化标签页图标
        tabIcons = new Texture2D[2];
        tabIcons[0] = EditorGUIUtility.FindTexture("TerrainInspector.TerrainToolSplat");
        tabIcons[1] = EditorGUIUtility.FindTexture("Prefab Icon");

        // 初始化标签页
        areaSelectorTab = new AreaSelectorTab();
        prefabGenerationTab = new PrefabGenerationTab();

        // 初始化标签页
        areaSelectorTab.Initialize();
        prefabGenerationTab.Initialize();

        // 注册场景视图事件
        SceneView.duringSceneGui += OnSceneGUI;
    }

    private void OnDisable()
    {
        // 取消注册场景视图事件
        SceneView.duringSceneGui -= OnSceneGUI;

        // 清理标签页
        areaSelectorTab.Cleanup();
        prefabGenerationTab.Cleanup();
    }

    private void OnGUI()
    {
        // 处理键盘事件
        HandleKeyboardEvents();

        DrawToolbar();

        // 使用滚动视图
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 绘制当前标签页内容
        switch (currentTab)
        {
            case ToolboxTab.AreaSelector:
                areaSelectorTab.OnGUI();
                break;
            case ToolboxTab.PrefabGeneration:
                prefabGenerationTab.OnGUI();
                break;
        }

        // 结束滚动视图
        EditorGUILayout.EndScrollView();

        // 底部操作栏
        DrawBottomBar();
    }

    // 绘制工具栏
    private void DrawToolbar()
    {
        EditorGUILayout.BeginVertical(EditorStyles.toolbar);

        // 标签页按钮
        EditorGUILayout.BeginHorizontal();

        GUIStyle tabButtonStyle = new GUIStyle(EditorStyles.toolbarButton);
        tabButtonStyle.fixedHeight = 30;
        tabButtonStyle.fontSize = 12;
        tabButtonStyle.fontStyle = FontStyle.Bold;
        tabButtonStyle.alignment = TextAnchor.MiddleCenter;

        // 区域选择标签页
        GUI.backgroundColor = currentTab == ToolboxTab.AreaSelector ? new Color(0.7f, 0.9f, 1f) : Color.white;
        if (GUILayout.Button(new GUIContent(" 区域选择", tabIcons[0]), tabButtonStyle))
        {
            currentTab = ToolboxTab.AreaSelector;
        }

        // 预制体生成标签页
        GUI.backgroundColor = currentTab == ToolboxTab.PrefabGeneration ? new Color(0.7f, 0.9f, 1f) : Color.white;
        if (GUILayout.Button(new GUIContent(" 预制体生成", tabIcons[1]), tabButtonStyle))
        {
            currentTab = ToolboxTab.PrefabGeneration;
        }

        GUI.backgroundColor = Color.white; // 重置背景色
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();

        // 标签页标题
        GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel);
        titleStyle.fontSize = 16;
        titleStyle.alignment = TextAnchor.MiddleCenter;

        string title = "";
        switch (currentTab)
        {
            case ToolboxTab.AreaSelector:
                title = "区域选择";
                break;
            case ToolboxTab.PrefabGeneration:
                title = "预制体生成";
                break;
        }

        EditorGUILayout.Space(5);
        EditorGUILayout.LabelField(title, titleStyle, GUILayout.Height(30));
        EditorGUILayout.Space(5);
    }

    // 绘制底部操作栏
    private void DrawBottomBar()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.BeginHorizontal();

        GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
        buttonStyle.fixedHeight = 35;
        buttonStyle.fontSize = 12;
        buttonStyle.fontStyle = FontStyle.Bold;
        buttonStyle.alignment = TextAnchor.MiddleCenter;
        buttonStyle.padding = new RectOffset(8, 8, 8, 8);
        buttonStyle.margin = new RectOffset(2, 2, 2, 2);

        // 根据当前标签页显示不同的按钮
        switch (currentTab)
        {
            case ToolboxTab.AreaSelector:
                GUILayout.FlexibleSpace();
                if (GUILayout.Button("下一步：预制体生成", buttonStyle, GUILayout.MinWidth(150)))
                {
                    currentTab = ToolboxTab.PrefabGeneration;
                }
                GUILayout.FlexibleSpace();
                break;
            case ToolboxTab.PrefabGeneration:
                // 上一步按钮
                if (GUILayout.Button("区域选择", buttonStyle, GUILayout.MinWidth(80)))
                {
                    currentTab = ToolboxTab.AreaSelector;
                }

                // 生成预制体按钮
                GUI.backgroundColor = new Color(0.4f, 0.8f, 0.4f);
                if (GUILayout.Button("生成预制体", buttonStyle, GUILayout.MinWidth(100)))
                {
                    GeneratePrefabs();
                }
                GUI.backgroundColor = Color.white;

                // 清除所有按钮
                GUI.backgroundColor = new Color(1f, 0.4f, 0.4f);
                if (GUILayout.Button("清除所有", buttonStyle, GUILayout.MinWidth(80)))
                {
                    prefabGenerationTab.ClearAllGeneratedPrefabs();
                }
                GUI.backgroundColor = Color.white;
                break;
        }

        EditorGUILayout.EndHorizontal();
    }

    // 生成预制体
    private void GeneratePrefabs()
    {
        // 获取选中的区域
        HashSet<Vector2Int> selectedCells = areaSelectorTab.GetSelectedCells();

        // 执行生成
        prefabGenerationTab.GeneratePrefabs(selectedCells);
    }

    // 获取区域选择标签页
    public AreaSelectorTab GetAreaSelector()
    {
        return areaSelectorTab;
    }

    // 场景GUI事件
    private void OnSceneGUI(SceneView sceneView)
    {
        // 根据当前标签页分发场景GUI事件
        switch (currentTab)
        {
            case ToolboxTab.AreaSelector:
                // 区域选择标签页：完整的交互功能
                areaSelectorTab.OnSceneGUI(sceneView, true);
                break;
            case ToolboxTab.PrefabGeneration:
                // 预制体生成标签页：只显示区域预览，不处理交互，使用预览模式
                areaSelectorTab.OnSceneGUI(sceneView, false, true);
                prefabGenerationTab.OnSceneGUI(sceneView);
                break;
        }
    }

    // 处理键盘事件
    private void HandleKeyboardEvents()
    {
        Event e = Event.current;
        if (e.type == EventType.KeyDown)
        {
            // Ctrl+Z 撤销
            if (e.control && e.keyCode == KeyCode.Z && !e.shift)
            {
                // 根据当前标签页分发撤销事件
                switch (currentTab)
                {
                    case ToolboxTab.AreaSelector:
                        if (areaSelectorTab.CanUndo())
                        {
                            areaSelectorTab.Undo();
                            e.Use(); // 消费事件，防止其他系统处理
                            Repaint(); // 重绘窗口
                        }
                        break;
                    case ToolboxTab.PrefabGeneration:
                        // 预制体生成的撤销由Unity Undo系统处理，这里不需要特殊处理
                        break;
                }
            }
            // Ctrl+Y 或 Ctrl+Shift+Z 重做
            else if ((e.control && e.keyCode == KeyCode.Y) ||
                     (e.control && e.shift && e.keyCode == KeyCode.Z))
            {
                // 根据当前标签页分发重做事件
                switch (currentTab)
                {
                    case ToolboxTab.AreaSelector:
                        if (areaSelectorTab.CanRedo())
                        {
                            areaSelectorTab.Redo();
                            e.Use(); // 消费事件，防止其他系统处理
                            Repaint(); // 重绘窗口
                        }
                        break;
                    case ToolboxTab.PrefabGeneration:
                        // 预制体生成的重做由Unity Undo系统处理，这里不需要特殊处理
                        break;
                }
            }
        }
    }
}
