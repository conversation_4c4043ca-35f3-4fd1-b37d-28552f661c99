using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;

public class PrefabGenerator
{
    public static IEnumerable<Transform> EnumerateTransform(Transform root)
    {
        yield return root;
        for (int i = 0; i < root.childCount; i++)
        {
            foreach (var item in EnumerateTransform(root.GetChild(i)))
            {
                yield return item;
            }
        }
    }

    const string MaterialFolderName = "Materials";
    const string PrefabFolderName = "Prefab";
    const string SkinFolderName = "Skin";

    [MenuItem("Assets/生成角色Prefab")]
    static void CharacterPrefabGenerate()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        string materialFolder = Path.Combine(path, MaterialFolderName);
        string prefabFolder = Path.Combine(path, PrefabFolderName);
        string skinFolder = Path.Combine(path, SkinFolderName);

        foreach (var modelGUID in AssetDatabase.FindAssets($"t:{nameof(GameObject)}", new string[] { skinFolder }))
        {
            string modelPath = AssetDatabase.GUIDToAssetPath(modelGUID);
            GameObject source = PrefabUtility.InstantiatePrefab(AssetDatabase.LoadAssetAtPath<GameObject>(modelPath)) as GameObject;

            // 找Material
            string[] materialGUIDs = AssetDatabase.FindAssets($"t:{nameof(Material)}", new string[] { materialFolder });
            string[] materialPaths = new string[materialGUIDs.Length];
            for (int i = 0; i < materialGUIDs.Length; i++)
            {
                materialPaths[i] = AssetDatabase.GUIDToAssetPath(materialGUIDs[i]);
            }

            Dictionary<string, Material> materialDic = new Dictionary<string, Material>();

            Material edgeMat = null;
            foreach (var materialPath in materialPaths)
            {
                Material mat = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                if (Regex.IsMatch(mat.name, ".+_Edge$"))
                    edgeMat = mat;
                else
                    materialDic[mat.name] = mat;
            }

            // 赋值Material
            foreach (var trans in EnumerateTransform(source.transform))
            {
                Renderer renderer = trans.GetComponent<Renderer>();
                if (renderer == null) continue;

                string matName = "M_" + renderer.name;
                if (materialDic.TryGetValue(matName, out Material mat))
                {
                    renderer.sharedMaterial = mat;
                    if (edgeMat != null)
                    {
                        Material[] materials = new Material[2];
                        renderer.sharedMaterials.CopyTo(materials, 0);
                        materials[1] = edgeMat;
                        renderer.sharedMaterials = materials;
                    }
                }
            }

            // 保存Prefab
            string name = string.Concat("P_", Regex.Replace(source.name, "_skin$", ""));
            string prefabPath = Path.Combine(prefabFolder, name + ".prefab");
            PrefabUtility.UnpackPrefabInstance(source, PrefabUnpackMode.Completely, InteractionMode.AutomatedAction);
            GameObject prefab = PrefabUtility.SaveAsPrefabAsset(source, prefabPath);

            // 清理场景
            Object.DestroyImmediate(source);
        }
    }

    [MenuItem("Assets/生成角色Prefab", validate = true)]
    static bool PrefabGenerateValidate()
    {
        // 限制只能选中一个
        if (Selection.objects.Length != 1)
            return false;

        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        // 限制只能文件夹
        if (!AssetDatabase.IsValidFolder(path))
            return false;

        return IsMatchedFolder(path);
    }

    static bool IsMatchedFolder(string path)
    {
        // 文件夹结构需符合条件
        // Materials Prefab Skin
        DirectoryInfo directoryInfo = Directory.CreateDirectory(path);
        DirectoryInfo[] childrens = directoryInfo.GetDirectories();
        DirectoryInfo materialFolder = childrens.FirstOrDefault(dir => dir.Name == "Materials");
        DirectoryInfo prefabFolder = childrens.FirstOrDefault(dir => dir.Name == "Prefab");
        DirectoryInfo skinFolder = childrens.FirstOrDefault(dir => dir.Name == "Skin");

        if (materialFolder == null
            || prefabFolder == null
            || skinFolder == null)
            return false;

        return true;
    }
}
