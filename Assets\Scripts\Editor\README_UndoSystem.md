# 区域选择Undo系统

## 🔧 功能概述

为区域选择工具添加了完整的撤销/重做功能，支持所有选择操作的历史记录和回退。

## 🎯 核心功能

### 1. 撤销/重做操作
- **撤销 (Undo)**: 回退到上一个选择状态
- **重做 (Redo)**: 前进到下一个选择状态
- **历史记录**: 最多保存50步操作历史

### 2. 快捷键支持
- **Ctrl+Z**: 撤销操作
- **Ctrl+Y**: 重做操作
- **Ctrl+Shift+Z**: 重做操作（备用快捷键）

### 3. UI界面
- **撤销按钮**: 显示"撤销 (Ctrl+Z)"，不可用时灰显
- **重做按钮**: 显示"重做 (Ctrl+Y)"，不可用时灰显
- **智能启用**: 按钮根据历史状态自动启用/禁用

## 🔧 技术实现

### 核心数据结构
```csharp
// Undo系统
private List<HashSet<Vector2Int>> undoHistory = new List<HashSet<Vector2Int>>();
private int currentUndoIndex = -1;
private const int MAX_UNDO_STEPS = 50;
```

### 状态保存机制
```csharp
public void SaveUndoState()
{
    // 创建当前选择的深拷贝
    HashSet<Vector2Int> currentState = new HashSet<Vector2Int>(selectedCells);

    // 如果当前不在历史的末尾，删除后面的历史
    if (currentUndoIndex < undoHistory.Count - 1)
    {
        undoHistory.RemoveRange(currentUndoIndex + 1, undoHistory.Count - currentUndoIndex - 1);
    }

    // 添加新状态
    undoHistory.Add(currentState);
    currentUndoIndex = undoHistory.Count - 1;

    // 限制历史记录数量
    if (undoHistory.Count > MAX_UNDO_STEPS)
    {
        undoHistory.RemoveAt(0);
        currentUndoIndex--;
    }
}
```

### 撤销/重做实现
```csharp
public void Undo()
{
    if (CanUndo())
    {
        currentUndoIndex--;
        selectedCells = new HashSet<Vector2Int>(undoHistory[currentUndoIndex]);
        SceneView.RepaintAll();
    }
}

public void Redo()
{
    if (CanRedo())
    {
        currentUndoIndex++;
        selectedCells = new HashSet<Vector2Int>(undoHistory[currentUndoIndex]);
        SceneView.RepaintAll();
    }
}
```

## 🎨 支持的操作

### 1. 点选模式 🖱️
- **单点选择**: 每次点击保存undo状态
- **拖动选择**: 拖动过程中不保存，避免过多历史
- **矩形选择**: 完成矩形选择时保存undo状态

### 2. 绘制模式 🖌️
- **开始绘制**: 鼠标按下时保存undo状态
- **拖动绘制**: 拖动过程中不保存，避免过多历史
- **画笔大小**: 支持所有画笔大小的undo

### 3. 擦除模式 🧹
- **单点擦除**: 每次点击保存undo状态
- **拖动擦除**: 拖动过程中不保存，避免过多历史
- **矩形擦除**: 完成矩形擦除时保存undo状态

### 4. 其他操作 🔧
- **清除所有选择**: 保存undo状态
- **加载选区**: 自动保存当前状态

## 🔍 智能历史管理

### 历史记录策略
```
操作类型          | 保存时机           | 原因
----------------|------------------|------------------
点击选择/擦除     | 鼠标按下时         | 每次操作都是独立的
拖动选择/擦除     | 不保存            | 避免过多历史记录
矩形选择/擦除     | 完成操作时         | 整个矩形是一个操作
绘制开始         | 鼠标按下时         | 开始新的绘制笔画
绘制拖动         | 不保存            | 连续笔画是一个操作
清除所有         | 操作前            | 重要操作需要撤销
```

### 历史记录限制
- **最大步数**: 50步历史记录
- **内存管理**: 超出限制时自动删除最旧记录
- **分支处理**: 新操作会删除当前位置后的所有历史

## 🎯 用户体验

### 直观的UI反馈
```csharp
// 按钮状态控制
GUI.enabled = CanUndo();  // 撤销按钮
GUI.enabled = CanRedo();  // 重做按钮
```

### 快捷键处理
```csharp
private void HandleKeyboardShortcuts(Event e)
{
    if (e.type == EventType.KeyDown)
    {
        // Ctrl+Z 撤销
        if (e.control && e.keyCode == KeyCode.Z && !e.shift)
        {
            if (CanUndo())
            {
                Undo();
                e.Use(); // 消费事件，防止其他系统处理
            }
        }
        // Ctrl+Y 或 Ctrl+Shift+Z 重做
        else if ((e.control && e.keyCode == KeyCode.Y) || 
                 (e.control && e.shift && e.keyCode == KeyCode.Z))
        {
            if (CanRedo())
            {
                Redo();
                e.Use(); // 消费事件，防止其他系统处理
            }
        }
    }
}
```

## 💡 使用场景

### 场景1: 精确选择
```
用户操作：
1. 点选几个单元格
2. 发现选错了一个
3. 按Ctrl+Z撤销最后一次点击
4. 重新选择正确的单元格

结果：快速纠正选择错误
```

### 场景2: 绘制修正
```
用户操作：
1. 使用画笔绘制区域
2. 绘制过头了
3. 按Ctrl+Z撤销整个绘制笔画
4. 重新绘制

结果：轻松修正绘制错误
```

### 场景3: 实验性操作
```
用户操作：
1. 尝试不同的选择方案
2. 使用撤销/重做在不同方案间切换
3. 找到最佳方案

结果：支持探索性工作流程
```

### 场景4: 意外清除
```
用户操作：
1. 意外点击"清除所有选择"
2. 立即按Ctrl+Z撤销
3. 恢复之前的选择

结果：防止意外操作造成的损失
```

## 🔧 集成方式

### 回调机制
```csharp
// 在AreaSelectorUtils中添加回调参数
public static void HandleMouseEvents(..., System.Action saveUndoStateCallback = null)

// 在关键操作点调用
saveUndoStateCallback?.Invoke();
```

### 无侵入式设计
- **现有代码**: 最小化修改现有逻辑
- **可选功能**: 回调参数为可选，不影响其他调用
- **性能友好**: 只在需要时创建历史记录

## 🎉 功能优势

### 1. 用户友好
- ✅ **标准快捷键**: 符合用户习惯的Ctrl+Z/Ctrl+Y
- ✅ **视觉反馈**: 按钮状态清楚显示可用性
- ✅ **即时响应**: 撤销/重做立即生效

### 2. 性能优化
- ✅ **智能保存**: 避免在拖动时过度保存历史
- ✅ **内存限制**: 自动管理历史记录数量
- ✅ **深拷贝**: 确保历史状态独立性

### 3. 工作流程支持
- ✅ **探索性操作**: 支持尝试不同选择方案
- ✅ **错误恢复**: 快速从错误操作中恢复
- ✅ **实验友好**: 鼓励用户尝试不同方法

### 4. 技术稳定性
- ✅ **事件消费**: 正确处理键盘事件，避免冲突
- ✅ **状态一致**: 确保UI状态与历史状态同步
- ✅ **边界处理**: 正确处理历史记录的边界情况

现在区域选择工具具有完整的撤销/重做功能，大大提升了用户体验和工作效率！
