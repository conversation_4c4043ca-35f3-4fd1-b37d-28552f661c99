# 预制体格数自动识别功能

## 🎯 功能概述

工具现在可以自动识别预制体父文件夹名称中的格数信息，并在预制体生成时使用这些信息。

## 📁 文件夹命名规范

### 格数标注格式
在文件夹名称中使用 `宽度x高度` 的格式来标注预制体的占格数：

```
Assets/Art/Environment/Prefabs/
├── Buildings_3x3/          # 3x3格的建筑
│   ├── House_01.prefab
│   ├── House_02.prefab
│   └── Shop_01.prefab
├── Trees_1x1/              # 1x1格的树木
│   ├── Oak_Tree.prefab
│   ├── Pine_Tree.prefab
│   └── Birch_Tree.prefab
├── LargeBuildings_5x5/     # 5x5格的大型建筑
│   ├── Castle.prefab
│   ├── Cathedral.prefab
│   └── Palace.prefab
└── Roads_2x4/              # 2x4格的道路
    ├── Straight_Road.prefab
    ├── Curved_Road.prefab
    └── Intersection.prefab
```

### 支持的格式
- `1x1` - 1格宽，1格高
- `2x3` - 2格宽，3格高
- `5x5` - 5格宽，5格高
- `10x2` - 10格宽，2格高

### 命名示例
- `Buildings_3x3` ✅ 正确
- `Trees_1x1` ✅ 正确
- `Houses_2x4_Medieval` ✅ 正确（可以有额外描述）
- `3x3_Buildings` ✅ 正确（格数可以在前面）
- `Buildings` ❌ 无格数信息（默认为1x1）

## 🔍 自动识别机制

### 解析逻辑
1. **获取预制体路径**: 获取预制体文件的完整路径
2. **提取父文件夹名**: 获取预制体所在的直接父文件夹名称
3. **正则表达式匹配**: 使用正则表达式 `(\d+)x(\d+)` 查找格数信息
4. **解析格数**: 提取宽度和高度数值
5. **默认值**: 如果没有找到格数信息，默认为1x1

### 技术实现
```csharp
// 解析文件夹名称中的格数信息
private Vector2Int ParseGridSizeFromFolderName(string folderName)
{
    var match = Regex.Match(folderName, @"(\d+)x(\d+)");
    
    if (match.Success)
    {
        int width = int.Parse(match.Groups[1].Value);
        int height = int.Parse(match.Groups[2].Value);
        return new Vector2Int(width, height);
    }
    
    return new Vector2Int(1, 1); // 默认1x1
}
```

## 🎨 界面显示

### 预制体浏览器中的显示
每个预制体预览下方会显示：
1. **预制体名称**: 如 "House_01"
2. **格数信息**: 如 "3x3"（灰色小字）

```
┌─────────────┐
│             │
│  预览图像   │
│             │
├─────────────┤
│  House_01   │  ← 预制体名称
│    3x3      │  ← 格数信息（灰色）
└─────────────┘
```

### 控制台调试信息
选择文件夹后，控制台会输出每个预制体的格数信息：
```
预制体: House_01, 格数: 3x3
预制体: House_02, 格数: 3x3
预制体: Shop_01, 格数: 3x3
预制体: Oak_Tree, 格数: 1x1
```

## 🔧 数据结构

### PrefabSetting 扩展
```csharp
public class PrefabSetting
{
    public GameObject prefab;
    public float weight = 1f;
    // ... 其他设置
    
    // 新增：预制体占格信息
    public int gridWidth = 1;   // 宽度（格数）
    public int gridHeight = 1;  // 高度（格数）
    public Vector2Int gridSize => new Vector2Int(gridWidth, gridHeight);
}
```

## 🎯 应用场景

### 1. 网格化生成
- 根据预制体的格数信息进行网格对齐生成
- 避免大型建筑重叠
- 确保道路和建筑的正确连接

### 2. 密度控制
- 大格数预制体（如5x5建筑）生成密度较低
- 小格数预制体（如1x1树木）可以密集生成

### 3. 布局优化
- 自动计算预制体之间的最小间距
- 根据格数信息优化场景布局

## 📋 使用流程

### 1. 准备文件夹结构
```
1. 创建文件夹并按格数命名
2. 将相同格数的预制体放入对应文件夹
3. 确保文件夹名称包含格数信息（如"3x3"）
```

### 2. 选择和预览
```
1. 打开预制体生成工具箱
2. 选择包含格数信息的文件夹
3. 在预制体浏览器中查看格数信息
4. 验证格数识别是否正确
```

### 3. 生成预制体
```
1. 配置生成参数
2. 工具会自动使用识别的格数信息
3. 生成时考虑预制体的实际占用空间
```

## 🔍 故障排除

### 格数识别不正确
**问题**: 预制体显示为1x1，但文件夹名称包含格数信息
**解决**: 
- 检查文件夹名称格式是否正确（如"3x3"）
- 确保使用小写字母"x"而不是大写"X"
- 查看控制台调试信息确认解析结果

### 文件夹命名建议
**推荐格式**:
- `Buildings_3x3`
- `Trees_1x1` 
- `Roads_2x4`

**避免格式**:
- `Buildings 3x3`（空格可能影响解析）
- `Buildings_3X3`（大写X）
- `Buildings_3*3`（错误符号）

## 🚀 未来扩展

### 计划中的功能
1. **智能间距**: 根据格数自动计算预制体间距
2. **网格对齐**: 强制预制体对齐到网格
3. **冲突检测**: 检测大型预制体的重叠问题
4. **布局算法**: 基于格数的智能布局算法

### 自定义格数
如果需要手动设置格数，可以在预制体设置中直接修改 `gridWidth` 和 `gridHeight` 值。

## 🎉 功能优势

- ✅ **自动识别**: 无需手动设置，从文件夹名称自动解析
- ✅ **可视化显示**: 在预制体浏览器中直观显示格数信息
- ✅ **调试友好**: 控制台输出详细的解析信息
- ✅ **向后兼容**: 没有格数信息的预制体默认为1x1
- ✅ **灵活命名**: 支持多种文件夹命名格式

现在工具可以智能识别每个预制体的占格数，为后续的智能生成功能奠定基础！
