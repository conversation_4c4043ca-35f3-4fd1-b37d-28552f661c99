# 编辑范围渲染框修复

## 🔧 问题定位

**问题**: 出现问题的应该是编辑范围的渲染框

**根本原因**: 编辑范围的渲染框没有使用统一的渲染逻辑，存在以下问题：
1. 完整模式的编辑范围没有设置深度测试
2. 完整模式的编辑范围没有高度偏移
3. 预览模式的编辑范围偏移不合适

## 🎯 修复的问题

### 1. 完整模式编辑范围 (AreaSelectorUtils.DrawGrid)

#### 修复前的问题 ❌
```csharp
// 没有深度测试设置
Handles.color = rangeColor;

// 没有高度偏移
Vector3[] corners = new Vector3[]
{
    new Vector3(startX * GRID_SIZE, cornerY1, startZ * GRID_SIZE),
    // ... 其他角点都是 cornerY + 0
};

// 直接绘制，没有深度控制
Handles.DrawSolidRectangleWithOutline(corners, fillColor, rangeColor);
```

#### 修复后的正确逻辑 ✅
```csharp
// 设置深度测试，与其他UI元素保持一致
Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
Handles.color = rangeColor;

// 添加适当的高度偏移
Vector3[] corners = new Vector3[]
{
    new Vector3(startX * GRID_SIZE, cornerY1 + 0.005f, startZ * GRID_SIZE),
    // ... 其他角点都是 cornerY + 0.005f
};

Handles.DrawSolidRectangleWithOutline(corners, fillColor, rangeColor);

// 恢复默认深度测试
Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
```

### 2. 预览模式编辑范围 (AreaSelectorUtils.DrawEditRangeBorder)

#### 修复前的偏移 ❌
```csharp
// 使用过高的偏移
Vector3[] corners = new Vector3[]
{
    new Vector3(startX * GRID_SIZE, cornerY1 + 0.01f, startZ * GRID_SIZE),
    // ... 偏移 0.01f 与选中区域冲突
};
```

#### 修复后的偏移 ✅
```csharp
// 使用更低的偏移，避免与其他元素冲突
Vector3[] corners = new Vector3[]
{
    new Vector3(startX * GRID_SIZE, cornerY1 + 0.005f, startZ * GRID_SIZE),
    // ... 偏移 0.005f 作为最底层
};
```

## 🔍 统一的层级系统

### 更新后的完整层级
```
深度层级 (从上到下):
1. 悬停指示器 (0.03f):
   - 🟢 绿色画笔指示器
   - 🔴 红色擦除指示器

2. 起始点和矩形预览 (0.02f):
   - 🟡 黄色矩形选择起始点
   - 🟡 黄色矩形选择预览
   - 🔴 红色擦除起始点
   - 🔴 红色擦除预览

3. 选中区域 (0.01f):
   - 🟢 绿色选中区域填充

4. 编辑范围边界 (0.005f):
   - 🟠 橙色编辑范围框 (完整模式)
   - 🟠 橙色编辑范围边框 (预览模式)
```

## 🎨 编辑范围的两种模式

### 完整模式 (区域选择标签页)
```csharp
// 在 AreaSelectorUtils.DrawGrid() 中
if (limitEditRange)
{
    // 设置深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;
    
    // 绘制半透明填充 + 边框
    Handles.DrawSolidRectangleWithOutline(corners, 
        new Color(rangeColor.r, rangeColor.g, rangeColor.b, 0.1f), 
        rangeColor);
    
    // 恢复深度测试
    Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
}
```

### 预览模式 (预制体生成标签页)
```csharp
// 在 AreaSelectorUtils.DrawEditRangeBorder() 中
// 设置深度测试
Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

// 只绘制边框，不绘制填充
Handles.DrawPolyLine(corners[0], corners[1], corners[2], corners[3], corners[0]);

// 恢复深度测试
Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
```

## 🔧 技术细节

### 深度测试统一
所有编辑范围元素现在都使用：
- **设置**: `CompareFunction.LessEqual`
- **恢复**: `CompareFunction.Always`

### 高度偏移策略
- **0.005f**: 编辑范围边界（最底层，不与其他元素冲突）
- **0.01f**: 选中区域填充
- **0.02f**: 起始点和矩形预览
- **0.03f**: 悬停指示器

### 地面检测集成
```csharp
// 编辑范围也使用相同的地面检测
float cornerY1 = GetGroundHeight(startX * GRID_SIZE, startZ * GRID_SIZE);
float cornerY2 = GetGroundHeight(endX * GRID_SIZE, startZ * GRID_SIZE);
// ... 其他角点

// 应用统一的偏移
Vector3[] corners = new Vector3[]
{
    new Vector3(startX * GRID_SIZE, cornerY1 + 0.005f, startZ * GRID_SIZE),
    // ...
};
```

## 🎯 修复效果

### 修复前的问题
- ❌ 编辑范围框可能遮挡其他UI元素
- ❌ 没有正确的深度关系
- ❌ 在某些情况下可能不可见或错误显示

### 修复后的效果
- ✅ 编辑范围框正确显示在最底层
- ✅ 与所有其他UI元素有正确的深度关系
- ✅ 使用统一的深度测试逻辑
- ✅ 适当的高度偏移避免冲突

## 💡 设计原理

### 为什么使用 0.005f 偏移？
- **最底层**: 编辑范围是背景性质的指示，应该在最底层
- **避免冲突**: 0.005f 确保不与 0.01f 的选中区域冲突
- **可见性**: 仍然高于地面，确保可见性

### 为什么需要深度测试？
- **一致性**: 与其他UI元素保持一致的渲染逻辑
- **正确遮挡**: 确保编辑范围不会错误地遮挡模型
- **视觉稳定**: 提供稳定可预测的视觉效果

## 🎉 最终效果

### 视觉层次清晰
- ✅ 编辑范围框作为背景指示，不干扰其他操作
- ✅ 所有UI元素有明确的前后层次关系
- ✅ 没有视觉冲突或重叠问题

### 渲染逻辑统一
- ✅ 编辑范围使用与其他元素相同的深度测试
- ✅ 统一的地面检测和高度偏移系统
- ✅ 一致的渲染流程和错误处理

### 功能完整性
- ✅ 完整模式：半透明填充 + 边框，清晰显示编辑范围
- ✅ 预览模式：只有边框，不干扰预制体查看
- ✅ 两种模式都有正确的深度关系

现在编辑范围的渲染框应该与所有其他UI元素完全一致，使用统一的渲染逻辑和正确的深度关系！
