using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// 区域选择标签页，用于在场景中选择区域
/// </summary>
public class AreaSelectorTab
{
    // 选择模式
    public enum SelectionMode
    {
        Point,      // 点选模式（包含矩形选择）
        Draw,       // 绘制模式
        Erase       // 擦除模式
    }

    // 当前选择模式
    private SelectionMode currentMode = SelectionMode.Point;

    // 网格大小（1米）
    private const float GRID_SIZE = 1f;

    // 选中的区域（以网格坐标存储）
    private HashSet<Vector2Int> selectedCells = new HashSet<Vector2Int>();

    // 矩形选择的起始点
    private Vector2Int rectangleStart;
    private bool isSelectingRectangle = false;
    private bool waitingForSecondClick = false;

    // 擦除矩形的起始点
    private Vector2Int eraseStart;
    private bool isSelectingEraseRect = false;
    private bool waitingForSecondEraseClick = false;

    // 显示网格
    private bool showGrid = true;

    // 网格颜色
    private Color gridColor = new Color(0.5f, 0.5f, 0.5f, 0.3f);

    // 选中区域颜色
    private Color selectedColor = new Color(0.2f, 0.8f, 0.2f, 0.5f);

    // 当前悬停的单元格
    private Vector2Int hoveredCell;

    // 上一个绘制的单元格（用于绘制模式）
    private Vector2Int lastDrawCell;

    // 绘制模式的画笔大小（半径，单位：单元格）
    private int brushSize = 1;

    // 场景视图控制
    private SceneView sceneView;

    // 是否限制编辑范围
    private bool limitEditRange = true;

    // 编辑范围左下角坐标
    private Vector2Int editRangeOrigin = Vector2Int.zero;

    // 编辑范围宽度和高度
    private int editRangeWidth = 100;
    private int editRangeHeight = 100;

    // 编辑范围颜色
    private Color rangeColor = new Color(1f, 0.5f, 0f, 0.5f); // 橙色

    // 折叠状态
    private bool showModeSettings = true;
    private bool showBrushSettings = true;
    private bool showRangeSettings = true;
    private bool showGridSettings = true;
    private bool showSelectionInfo = true;

    // Undo系统
    private List<HashSet<Vector2Int>> undoHistory = new List<HashSet<Vector2Int>>();
    private int currentUndoIndex = -1;
    private const int MAX_UNDO_STEPS = 50;

    // 初始化
    public void Initialize()
    {
        sceneView = SceneView.lastActiveSceneView;
        // 初始化undo系统，保存初始状态
        SaveUndoState();
    }

    // 清理
    public void Cleanup()
    {
        // 清理资源
    }

    // 绘制UI
    public void OnGUI()
    {
        // 选择模式设置（使用折叠面板）
        showModeSettings = EditorGUILayout.Foldout(showModeSettings, "选择模式设置", true, EditorStyles.foldoutHeader);

        if (showModeSettings)
        {
            EditorGUI.indentLevel++;

            // 工具按钮布局
            EditorGUILayout.BeginHorizontal();

            GUIStyle toolButtonStyle = new GUIStyle(GUI.skin.button);
            toolButtonStyle.fixedWidth = 80;
            toolButtonStyle.fixedHeight = 30;

            // 点选模式按钮
            GUI.backgroundColor = currentMode == SelectionMode.Point ? new Color(0.6f, 1f, 0.6f) : Color.white;
            if (GUILayout.Button(new GUIContent("点选", "点选或拖动添加单元格，按住Shift键进行矩形选择"), toolButtonStyle))
            {
                currentMode = SelectionMode.Point;
            }

            // 绘制模式按钮
            GUI.backgroundColor = currentMode == SelectionMode.Draw ? new Color(0.6f, 1f, 0.6f) : Color.white;
            if (GUILayout.Button(new GUIContent("绘制", "自由绘制添加单元格"), toolButtonStyle))
            {
                currentMode = SelectionMode.Draw;
            }

            // 擦除模式按钮
            GUI.backgroundColor = currentMode == SelectionMode.Erase ? new Color(0.6f, 1f, 0.6f) : Color.white;
            if (GUILayout.Button(new GUIContent("擦除", "移除单元格 (按住Shift进行矩形擦除)"), toolButtonStyle))
            {
                currentMode = SelectionMode.Erase;
            }

            GUI.backgroundColor = Color.white; // 重置背景色
            EditorGUILayout.EndHorizontal();

            // 当前模式说明
            string modeDescription = "";
            switch (currentMode)
            {
                case SelectionMode.Point:
                    if (Event.current.shift && waitingForSecondClick)
                        modeDescription = "点击第二个点完成矩形选区";
                    else if (Event.current.shift)
                        modeDescription = "按住Shift键并点击第一个点开始矩形选区，再点击第二个点完成选区";
                    else
                        modeDescription = "点选或拖动鼠标添加单元格，按住Shift键可进行矩形选择";
                    break;
                case SelectionMode.Draw:
                    modeDescription = "按住鼠标左键并拖动，以画笔方式自由绘制选区";
                    break;
                case SelectionMode.Erase:
                    if (waitingForSecondEraseClick)
                        modeDescription = "点击第二个点完成擦除矩形区域";
                    else
                        modeDescription = "直接点击/拖动擦除单个单元格，或按住Shift键点击第一个点开始矩形擦除";
                    break;
            }

            EditorGUILayout.HelpBox(modeDescription, MessageType.Info);

            // 快捷键提示
            EditorGUILayout.Space(5);
            EditorGUILayout.HelpBox("快捷键提示:\n• 在点选模式下按住Shift键可以进行矩形选择\n• 在擦除模式下按住Shift键可以进行矩形擦除\n• 右键点击可以取消当前操作", MessageType.Info);

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space(10);

        // 绘制模式设置
        if (currentMode == SelectionMode.Draw)
        {
            showBrushSettings = EditorGUILayout.Foldout(showBrushSettings, "画笔设置", true, EditorStyles.foldoutHeader);

            if (showBrushSettings)
            {
                EditorGUI.indentLevel++;

                // 画笔大小滑动条
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PrefixLabel("画笔大小");

                int newBrushSize = EditorGUILayout.IntSlider(brushSize, 1, 5);
                if (newBrushSize != brushSize)
                {
                    brushSize = newBrushSize;
                    SceneView.RepaintAll();
                }

                EditorGUILayout.EndHorizontal();

                // 显示画笔大小说明
                string brushSizeDesc = "";
                switch (brushSize)
                {
                    case 1:
                        brushSizeDesc = "小 (1x1)";
                        break;
                    case 2:
                        brushSizeDesc = "中 (2x2)";
                        break;
                    case 3:
                        brushSizeDesc = "较大 (3x3)";
                        break;
                    case 4:
                        brushSizeDesc = "大 (4x4)";
                        break;
                    case 5:
                        brushSizeDesc = "最大 (5x5)";
                        break;
                }

                EditorGUILayout.HelpBox($"当前画笔大小: {brushSizeDesc}", MessageType.Info);

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space(10);
        }

        // 编辑范围设置
        showRangeSettings = EditorGUILayout.Foldout(showRangeSettings, "编辑范围设置", true, EditorStyles.foldoutHeader);

        if (showRangeSettings)
        {
            EditorGUI.indentLevel++;

            // 是否限制编辑范围
            bool newLimitEditRange = EditorGUILayout.Toggle("限制编辑范围", limitEditRange);
            if (newLimitEditRange != limitEditRange)
            {
                limitEditRange = newLimitEditRange;
                SceneView.RepaintAll();
            }

            // 编辑范围颜色设置
            Color newRangeColor = EditorGUILayout.ColorField("编辑范围颜色", rangeColor);
            if (newRangeColor != rangeColor)
            {
                rangeColor = newRangeColor;
                SceneView.RepaintAll();
            }

            if (limitEditRange)
            {
                EditorGUI.indentLevel++;

                // 编辑范围左下角坐标
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PrefixLabel("左下角坐标");

                EditorGUILayout.LabelField("X:", GUILayout.Width(15));
                int newOriginX = EditorGUILayout.IntField(editRangeOrigin.x, GUILayout.Width(50));

                EditorGUILayout.LabelField("Y:", GUILayout.Width(15));
                int newOriginY = EditorGUILayout.IntField(editRangeOrigin.y, GUILayout.Width(50));

                if (newOriginX != editRangeOrigin.x || newOriginY != editRangeOrigin.y)
                {
                    editRangeOrigin = new Vector2Int(newOriginX, newOriginY);
                    SceneView.RepaintAll();
                }

                EditorGUILayout.EndHorizontal();

                // 按钮行
                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button("重置坐标", GUILayout.Height(24)))
                {
                    editRangeOrigin = Vector2Int.zero;
                    SceneView.RepaintAll();
                }

                if (GUILayout.Button("设为当前位置", GUILayout.Height(24)))
                {
                    // 确保有有效的场景视图
                    if (sceneView == null)
                    {
                        sceneView = SceneView.lastActiveSceneView;
                    }

                    if (sceneView != null && sceneView.camera != null)
                    {
                        // 获取场景视图相机位置
                        Vector3 cameraPos = sceneView.camera.transform.position;
                        editRangeOrigin = new Vector2Int(
                            Mathf.RoundToInt(cameraPos.x / GRID_SIZE) - editRangeWidth / 2,
                            Mathf.RoundToInt(cameraPos.z / GRID_SIZE) - editRangeHeight / 2
                        );
                        SceneView.RepaintAll();
                    }
                    else
                    {
                        Debug.LogWarning("无法获取场景视图相机位置");
                    }
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                // 编辑范围宽度和高度
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PrefixLabel("范围大小");

                EditorGUILayout.LabelField("宽:", GUILayout.Width(20));
                int newWidth = EditorGUILayout.IntField(editRangeWidth, GUILayout.Width(50));

                EditorGUILayout.LabelField("高:", GUILayout.Width(20));
                int newHeight = EditorGUILayout.IntField(editRangeHeight, GUILayout.Width(50));

                if (newWidth != editRangeWidth || newHeight != editRangeHeight)
                {
                    editRangeWidth = Mathf.Max(10, newWidth);
                    editRangeHeight = Mathf.Max(10, newHeight);
                    SceneView.RepaintAll();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUI.indentLevel--;
            }

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space(10);

        // 选中区域信息和操作
        showSelectionInfo = EditorGUILayout.Foldout(showSelectionInfo, "选区操作", true, EditorStyles.foldoutHeader);

        if (showSelectionInfo)
        {
            EditorGUI.indentLevel++;

            // 选中区域信息
            EditorGUILayout.LabelField($"已选中 {selectedCells.Count} 个单元格", EditorStyles.boldLabel);

            EditorGUILayout.Space(5);

            // 操作按钮
            GUIStyle actionButtonStyle = new GUIStyle(GUI.skin.button);
            actionButtonStyle.fontStyle = FontStyle.Bold;
            actionButtonStyle.fixedHeight = 30;

            // Undo/Redo按钮行
            EditorGUILayout.BeginHorizontal();

            // Undo按钮
            GUI.enabled = CanUndo();
            if (GUILayout.Button("撤销", actionButtonStyle))
            {
                Undo();
            }

            // Redo按钮
            GUI.enabled = CanRedo();
            if (GUILayout.Button("重做", actionButtonStyle))
            {
                Redo();
            }

            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            if (GUILayout.Button("清除所有选择", actionButtonStyle))
            {
                SaveUndoState(); // 保存状态用于撤销
                selectedCells.Clear();
                SceneView.RepaintAll();
            }

            // 保存和加载选区
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("保存选区", GUILayout.Height(30)))
            {
                SaveSelection();
            }
            if (GUILayout.Button("加载选区", GUILayout.Height(30)))
            {
                LoadSelection();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space(10);
    }

    // 保存当前选区
    private void SaveSelection()
    {
        string path = EditorUtility.SaveFilePanel("保存选区", Application.dataPath, "AreaSelection", "json");
        if (string.IsNullOrEmpty(path)) return;

        List<Vector2Int> selectionList = new List<Vector2Int>(selectedCells);
        string json = JsonUtility.ToJson(new SerializableSelection { cells = selectionList });
        System.IO.File.WriteAllText(path, json);
        Debug.Log($"选区已保存到: {path}");
    }

    // 加载选区
    private void LoadSelection()
    {
        string path = EditorUtility.OpenFilePanel("加载选区", Application.dataPath, "json");
        if (string.IsNullOrEmpty(path)) return;

        string json = System.IO.File.ReadAllText(path);
        SerializableSelection selection = JsonUtility.FromJson<SerializableSelection>(json);

        selectedCells.Clear();
        foreach (Vector2Int cell in selection.cells)
        {
            selectedCells.Add(cell);
        }

        SceneView.RepaintAll();
        Debug.Log($"已加载选区，包含 {selectedCells.Count} 个单元格");
    }

    // 用于序列化的辅助类
    [System.Serializable]
    private class SerializableSelection
    {
        public List<Vector2Int> cells = new List<Vector2Int>();
    }

    // 获取选中的单元格
    public HashSet<Vector2Int> GetSelectedCells()
    {
        return new HashSet<Vector2Int>(selectedCells);
    }

    // 场景GUI事件
    public void OnSceneGUI(SceneView sceneView)
    {
        OnSceneGUI(sceneView, true);
    }

    // 场景GUI事件（可控制是否处理交互）
    public void OnSceneGUI(SceneView sceneView, bool handleInteraction)
    {
        OnSceneGUI(sceneView, handleInteraction, false);
    }

    // 场景GUI事件（完整控制）
    public void OnSceneGUI(SceneView sceneView, bool handleInteraction, bool previewOnly)
    {
        // 保存当前场景视图引用
        this.sceneView = sceneView;

        // 获取事件
        Event e = Event.current;

        if (handleInteraction)
        {
            // 处理键盘快捷键
            HandleKeyboardShortcuts(e);

            // 在绘制模式下，阻止Unity的默认选择行为
            if (currentMode == SelectionMode.Draw)
            {
                // 阻止鼠标左键按下和拖动事件的默认处理
                if ((e.type == EventType.MouseDown || e.type == EventType.MouseDrag) && e.button == 0)
                {
                    // 告诉Unity我们要处理这个事件
                    int controlID = GUIUtility.GetControlID(FocusType.Passive);
                    GUIUtility.hotControl = controlID;
                }
                else if (e.type == EventType.MouseUp && e.button == 0)
                {
                    // 释放控制
                    GUIUtility.hotControl = 0;
                }
            }

            // 计算当前鼠标位置对应的网格坐标
            Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);
            Plane groundPlane = new Plane(Vector3.up, Vector3.zero);

            float distance;
            if (groundPlane.Raycast(ray, out distance))
            {
                Vector3 worldPoint = ray.GetPoint(distance);
                hoveredCell = new Vector2Int(
                    Mathf.FloorToInt(worldPoint.x / GRID_SIZE),
                    Mathf.FloorToInt(worldPoint.z / GRID_SIZE)
                );

                // 处理鼠标事件
                HandleMouseEvents(e);
            }

            // 强制重绘场景视图
            if (e.type == EventType.MouseMove || e.type == EventType.MouseDrag ||
                e.type == EventType.KeyDown || e.type == EventType.KeyUp)
            {
                sceneView.Repaint();
            }
        }

        // 绘制网格和选中区域
        if (e.type == EventType.Repaint)
        {
            if (previewOnly)
            {
                // 预览模式：只绘制边框，不绘制填充
                DrawGridPreview();
                DrawSelectedCellsPreview();
            }
            else
            {
                // 完整模式：绘制所有内容
                DrawGrid();
                DrawSelectedCells();
            }
        }
    }

    // 处理鼠标事件
    private void HandleMouseEvents(Event e)
    {
        AreaSelectorUtils.HandleMouseEvents(e, sceneView, ref hoveredCell, ref lastDrawCell,
            ref selectedCells, ref rectangleStart, ref isSelectingRectangle, ref waitingForSecondClick,
            ref eraseStart, ref isSelectingEraseRect, ref waitingForSecondEraseClick,
            currentMode, brushSize, limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight,
            SaveUndoState); // 传递undo回调
    }

    // 绘制网格
    private void DrawGrid()
    {
        AreaSelectorUtils.DrawGrid(sceneView, showGrid, gridColor, rangeColor,
            limitEditRange, editRangeOrigin, editRangeWidth, editRangeHeight);
    }

    // 绘制选中的单元格
    private void DrawSelectedCells()
    {
        // 绘制选中的单元格
        foreach (Vector2Int cell in selectedCells)
        {
            // 使用地面检测获取正确的Y坐标
            float groundY = GetGroundHeight(cell.x * GRID_SIZE + GRID_SIZE * 0.5f, cell.y * GRID_SIZE + GRID_SIZE * 0.5f);

            Vector3[] corners = new Vector3[]
            {
                new Vector3(cell.x * GRID_SIZE, groundY, cell.y * GRID_SIZE),
                new Vector3((cell.x + 1) * GRID_SIZE, groundY, cell.y * GRID_SIZE),
                new Vector3((cell.x + 1) * GRID_SIZE, groundY, (cell.y + 1) * GRID_SIZE),
                new Vector3(cell.x * GRID_SIZE, groundY, (cell.y + 1) * GRID_SIZE)
            };

            // 使用统一的渲染方法，选中区域使用最低层级
            DrawUIElement(corners, selectedColor, Color.clear, 0.01f);
        }

        // 绘制正在选择的矩形
        if (isSelectingRectangle || waitingForSecondClick)
        {
            // 获取起始点的地面高度
            float startGroundY = GetGroundHeight((rectangleStart.x + 0.5f) * GRID_SIZE, (rectangleStart.y + 0.5f) * GRID_SIZE);

            // 绘制起始点标记
            Vector3 startCenter = new Vector3(
                (rectangleStart.x + 0.5f) * GRID_SIZE,
                startGroundY,
                (rectangleStart.y + 0.5f) * GRID_SIZE
            );

            // 使用统一的标记渲染方法
            DrawUIMarker(startCenter, Color.yellow, GRID_SIZE * 0.25f, 0.02f);

            // 如果鼠标正在移动，绘制预览矩形
            if (isSelectingRectangle)
            {
                // 计算矩形区域
                int minX = Mathf.Min(rectangleStart.x, hoveredCell.x);
                int maxX = Mathf.Max(rectangleStart.x, hoveredCell.x);
                int minY = Mathf.Min(rectangleStart.y, hoveredCell.y);
                int maxY = Mathf.Max(rectangleStart.y, hoveredCell.y);

                // 如果限制编辑范围，限制矩形范围
                if (limitEditRange)
                {
                    minX = Mathf.Max(minX, editRangeOrigin.x);
                    maxX = Mathf.Min(maxX, editRangeOrigin.x + editRangeWidth);
                    minY = Mathf.Max(minY, editRangeOrigin.y);
                    maxY = Mathf.Min(maxY, editRangeOrigin.y + editRangeHeight);
                }

                // 获取四个角的地面高度
                float cornerY1 = GetGroundHeight(minX * GRID_SIZE, minY * GRID_SIZE);
                float cornerY2 = GetGroundHeight(maxX * GRID_SIZE + GRID_SIZE, minY * GRID_SIZE);
                float cornerY3 = GetGroundHeight(maxX * GRID_SIZE + GRID_SIZE, maxY * GRID_SIZE + GRID_SIZE);
                float cornerY4 = GetGroundHeight(minX * GRID_SIZE, maxY * GRID_SIZE + GRID_SIZE);

                Vector3[] corners = new Vector3[]
                {
                    new Vector3(minX * GRID_SIZE, cornerY1, minY * GRID_SIZE),
                    new Vector3(maxX * GRID_SIZE + GRID_SIZE, cornerY2, minY * GRID_SIZE),
                    new Vector3(maxX * GRID_SIZE + GRID_SIZE, cornerY3, maxY * GRID_SIZE + GRID_SIZE),
                    new Vector3(minX * GRID_SIZE, cornerY4, maxY * GRID_SIZE + GRID_SIZE)
                };

                // 使用统一的渲染方法
                DrawUIElement(corners, new Color(1f, 1f, 0f, 0.2f), Color.yellow, 0.02f);

                // 显示矩形大小信息
                Handles.BeginGUI();
                GUI.backgroundColor = new Color(0, 0, 0, 0.7f);
                GUI.contentColor = Color.white;

                GUIStyle style = new GUIStyle(EditorStyles.helpBox);
                style.normal.textColor = Color.yellow;
                style.fontSize = 12;
                style.fontStyle = FontStyle.Bold;
                style.alignment = TextAnchor.MiddleCenter;

                int width = maxX - minX + 1;
                int height = maxY - minY + 1;
                int area = width * height;

                Rect rect = new Rect(10, 70, 200, 25);
                GUI.Box(rect, $"选区大小: {width} x {height} = {area} 个单元格", style);

                Handles.EndGUI();
            }
        }

        // 绘制正在擦除的矩形
        if (isSelectingEraseRect || waitingForSecondEraseClick)
        {
            // 获取起始点的地面高度
            float startGroundY = GetGroundHeight((eraseStart.x + 0.5f) * GRID_SIZE, (eraseStart.y + 0.5f) * GRID_SIZE);

            // 绘制起始点标记
            Vector3 startCenter = new Vector3(
                (eraseStart.x + 0.5f) * GRID_SIZE,
                startGroundY,
                (eraseStart.y + 0.5f) * GRID_SIZE
            );

            // 使用统一的标记渲染方法
            DrawUIMarker(startCenter, Color.red, GRID_SIZE * 0.25f, 0.02f);

            // 如果鼠标正在移动，绘制预览矩形
            if (isSelectingEraseRect)
            {
                // 计算矩形区域
                int minX = Mathf.Min(eraseStart.x, hoveredCell.x);
                int maxX = Mathf.Max(eraseStart.x, hoveredCell.x);
                int minY = Mathf.Min(eraseStart.y, hoveredCell.y);
                int maxY = Mathf.Max(eraseStart.y, hoveredCell.y);

                // 如果限制编辑范围，限制矩形范围
                if (limitEditRange)
                {
                    minX = Mathf.Max(minX, editRangeOrigin.x);
                    maxX = Mathf.Min(maxX, editRangeOrigin.x + editRangeWidth);
                    minY = Mathf.Max(minY, editRangeOrigin.y);
                    maxY = Mathf.Min(maxY, editRangeOrigin.y + editRangeHeight);
                }

                // 获取四个角的地面高度
                float cornerY1 = GetGroundHeight(minX * GRID_SIZE, minY * GRID_SIZE);
                float cornerY2 = GetGroundHeight(maxX * GRID_SIZE + GRID_SIZE, minY * GRID_SIZE);
                float cornerY3 = GetGroundHeight(maxX * GRID_SIZE + GRID_SIZE, maxY * GRID_SIZE + GRID_SIZE);
                float cornerY4 = GetGroundHeight(minX * GRID_SIZE, maxY * GRID_SIZE + GRID_SIZE);

                Vector3[] corners = new Vector3[]
                {
                    new Vector3(minX * GRID_SIZE, cornerY1, minY * GRID_SIZE),
                    new Vector3(maxX * GRID_SIZE + GRID_SIZE, cornerY2, minY * GRID_SIZE),
                    new Vector3(maxX * GRID_SIZE + GRID_SIZE, cornerY3, maxY * GRID_SIZE + GRID_SIZE),
                    new Vector3(minX * GRID_SIZE, cornerY4, maxY * GRID_SIZE + GRID_SIZE)
                };

                // 使用统一的渲染方法
                DrawUIElement(corners, new Color(1f, 0f, 0f, 0.2f), Color.red, 0.02f);

                // 显示矩形大小信息
                Handles.BeginGUI();
                GUI.backgroundColor = new Color(0, 0, 0, 0.7f);
                GUI.contentColor = Color.white;

                GUIStyle style = new GUIStyle(EditorStyles.helpBox);
                style.normal.textColor = Color.red;
                style.fontSize = 12;
                style.fontStyle = FontStyle.Bold;
                style.alignment = TextAnchor.MiddleCenter;

                int width = maxX - minX + 1;
                int height = maxY - minY + 1;
                int area = width * height;

                Rect rect = new Rect(10, 70, 200, 25);
                GUI.Box(rect, $"擦除区域: {width} x {height} = {area} 个单元格", style);

                Handles.EndGUI();
            }
        }

        // 如果当前是绘制模式，显示画笔指示器
        if (currentMode == SelectionMode.Draw)
        {
            // 计算画笔范围的起始点和结束点
            int halfSize = brushSize / 2;
            int startX, startY, endX, endY;

            // 根据画笔大小是奇数还是偶数，计算不同的起始点和结束点
            if (brushSize % 2 == 1) // 奇数大小 (1x1, 3x3, 5x5)
            {
                startX = hoveredCell.x - halfSize;
                startY = hoveredCell.y - halfSize;
                endX = hoveredCell.x + halfSize;
                endY = hoveredCell.y + halfSize;
            }
            else // 偶数大小 (2x2, 4x4)
            {
                startX = hoveredCell.x - halfSize + 1;
                startY = hoveredCell.y - halfSize + 1;
                endX = hoveredCell.x + halfSize;
                endY = hoveredCell.y + halfSize;
            }

            // 获取四个角的地面高度
            float cornerY1 = GetGroundHeight(startX * GRID_SIZE, startY * GRID_SIZE);
            float cornerY2 = GetGroundHeight(endX * GRID_SIZE + GRID_SIZE, startY * GRID_SIZE);
            float cornerY3 = GetGroundHeight(endX * GRID_SIZE + GRID_SIZE, endY * GRID_SIZE + GRID_SIZE);
            float cornerY4 = GetGroundHeight(startX * GRID_SIZE, endY * GRID_SIZE + GRID_SIZE);

            // 计算方形的四个角
            Vector3[] corners = new Vector3[]
            {
                new Vector3(startX * GRID_SIZE, cornerY1, startY * GRID_SIZE),
                new Vector3(endX * GRID_SIZE + GRID_SIZE, cornerY2, startY * GRID_SIZE),
                new Vector3(endX * GRID_SIZE + GRID_SIZE, cornerY3, endY * GRID_SIZE + GRID_SIZE),
                new Vector3(startX * GRID_SIZE, cornerY4, endY * GRID_SIZE + GRID_SIZE)
            };

            // 使用统一的渲染方法，画笔指示器使用最高层级
            DrawUIElement(corners, new Color(0.2f, 0.8f, 0.2f, 0.3f), Color.white, 0.03f);
        }

        // 如果当前是擦除模式，显示擦除指示器
        if (currentMode == SelectionMode.Erase && !isSelectingEraseRect && !waitingForSecondEraseClick)
        {
            // 获取当前悬停格子的地面高度
            float groundY = GetGroundHeight((hoveredCell.x + 0.5f) * GRID_SIZE, (hoveredCell.y + 0.5f) * GRID_SIZE);

            // 绘制红色擦除指示器
            Vector3[] corners = new Vector3[]
            {
                new Vector3(hoveredCell.x * GRID_SIZE, groundY, hoveredCell.y * GRID_SIZE),
                new Vector3((hoveredCell.x + 1) * GRID_SIZE, groundY, hoveredCell.y * GRID_SIZE),
                new Vector3((hoveredCell.x + 1) * GRID_SIZE, groundY, (hoveredCell.y + 1) * GRID_SIZE),
                new Vector3(hoveredCell.x * GRID_SIZE, groundY, (hoveredCell.y + 1) * GRID_SIZE)
            };

            // 使用统一的渲染方法，擦除指示器使用最高层级
            DrawUIElement(corners, new Color(1f, 0.2f, 0.2f, 0.3f), Color.red, 0.03f);
        }
    }

    // 绘制网格预览版本（轻量化）
    private void DrawGridPreview()
    {
        // 只绘制编辑范围边界，不绘制网格线
        if (limitEditRange)
        {
            AreaSelectorUtils.DrawEditRangeBorder(rangeColor, editRangeOrigin, editRangeWidth, editRangeHeight);
        }
    }

    // 绘制选中格子的预览版本（只有边框，无填充）
    private void DrawSelectedCellsPreview()
    {
        if (selectedCells.Count == 0) return;

        // 设置深度测试，确保边框绘制在地面上
        Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

        // 只绘制选中区域的边框，不绘制填充
        Handles.color = new Color(0, 0.8f, 0, 0.8f); // 深绿色边框，稍微透明

        foreach (Vector2Int cell in selectedCells)
        {
            // 使用地面检测获取正确的Y坐标
            float groundY = GetGroundHeight(cell.x * GRID_SIZE + GRID_SIZE * 0.5f, cell.y * GRID_SIZE + GRID_SIZE * 0.5f);

            Vector3[] corners = new Vector3[]
            {
                new Vector3(cell.x * GRID_SIZE, groundY + 0.01f, cell.y * GRID_SIZE),
                new Vector3((cell.x + 1) * GRID_SIZE, groundY + 0.01f, cell.y * GRID_SIZE),
                new Vector3((cell.x + 1) * GRID_SIZE, groundY + 0.01f, (cell.y + 1) * GRID_SIZE),
                new Vector3(cell.x * GRID_SIZE, groundY + 0.01f, (cell.y + 1) * GRID_SIZE)
            };

            // 只绘制边框，不绘制填充
            Handles.DrawPolyLine(corners[0], corners[1], corners[2], corners[3], corners[0]);
        }

        // 恢复默认深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
    }

    // 获取地面高度
    private float GetGroundHeight(float x, float z)
    {
        // 从上方向下发射射线检测地面
        Vector3 rayStart = new Vector3(x, 1000f, z);
        Vector3 rayDirection = Vector3.down;

        if (Physics.Raycast(rayStart, rayDirection, out RaycastHit hit, 2000f))
        {
            return hit.point.y;
        }

        // 如果没有检测到地面，返回0
        return 0f;
    }

    // ===== Undo系统方法 =====

    // 保存当前状态到undo历史
    public void SaveUndoState()
    {
        // 创建当前选择的深拷贝
        HashSet<Vector2Int> currentState = new HashSet<Vector2Int>(selectedCells);

        // 如果当前不在历史的末尾，删除后面的历史
        if (currentUndoIndex < undoHistory.Count - 1)
        {
            undoHistory.RemoveRange(currentUndoIndex + 1, undoHistory.Count - currentUndoIndex - 1);
        }

        // 添加新状态
        undoHistory.Add(currentState);
        currentUndoIndex = undoHistory.Count - 1;

        // 限制历史记录数量
        if (undoHistory.Count > MAX_UNDO_STEPS)
        {
            undoHistory.RemoveAt(0);
            currentUndoIndex--;
        }
    }

    // 撤销操作
    public void Undo()
    {
        if (CanUndo())
        {
            currentUndoIndex--;
            selectedCells = new HashSet<Vector2Int>(undoHistory[currentUndoIndex]);
            SceneView.RepaintAll();
        }
    }

    // 重做操作
    public void Redo()
    {
        if (CanRedo())
        {
            currentUndoIndex++;
            selectedCells = new HashSet<Vector2Int>(undoHistory[currentUndoIndex]);
            SceneView.RepaintAll();
        }
    }

    // 检查是否可以撤销
    public bool CanUndo()
    {
        return currentUndoIndex > 0;
    }

    // 检查是否可以重做
    public bool CanRedo()
    {
        return currentUndoIndex < undoHistory.Count - 1;
    }

    // 处理键盘快捷键
    private void HandleKeyboardShortcuts(Event e)
    {
        if (e.type == EventType.KeyDown)
        {
            // Ctrl+Z 撤销
            if (e.control && e.keyCode == KeyCode.Z && !e.shift)
            {
                if (CanUndo())
                {
                    Undo();
                    e.Use(); // 消费事件，防止其他系统处理
                }
            }
            // Ctrl+Y 或 Ctrl+Shift+Z 重做
            else if ((e.control && e.keyCode == KeyCode.Y) ||
                     (e.control && e.shift && e.keyCode == KeyCode.Z))
            {
                if (CanRedo())
                {
                    Redo();
                    e.Use(); // 消费事件，防止其他系统处理
                }
            }
        }
    }

    // 统一的UI元素渲染方法
    private void DrawUIElement(Vector3[] corners, Color fillColor, Color outlineColor, float heightOffset)
    {
        // 设置统一的深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

        // 应用高度偏移
        for (int i = 0; i < corners.Length; i++)
        {
            corners[i] = new Vector3(corners[i].x, corners[i].y + heightOffset, corners[i].z);
        }

        // 绘制UI元素
        Handles.color = fillColor;
        Handles.DrawSolidRectangleWithOutline(corners, fillColor, outlineColor);

        // 恢复默认深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
    }

    // 统一的圆点标记渲染方法
    private void DrawUIMarker(Vector3 center, Color color, float radius, float heightOffset)
    {
        // 设置统一的深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.LessEqual;

        // 应用高度偏移
        center = new Vector3(center.x, center.y + heightOffset, center.z);

        // 绘制圆点标记
        Handles.color = color;
        Handles.DrawSolidDisc(center, Vector3.up, radius);

        // 恢复默认深度测试
        Handles.zTest = UnityEngine.Rendering.CompareFunction.Always;
    }
}
